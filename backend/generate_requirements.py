#!/usr/bin/env python3
"""
Script to generate actual requirements.txt based on currently installed packages
and what's actually being used in the codebase.
"""

import os
import sys
import subprocess
import ast
import re
from pathlib import Path
from typing import Set, Dict, List

def get_installed_packages() -> Dict[str, str]:
    """Get currently installed packages and their versions."""
    try:
        result = subprocess.run([sys.executable, '-m', 'pip', 'list', '--format=freeze'], 
                              capture_output=True, text=True, check=True)
        packages = {}
        for line in result.stdout.strip().split('\n'):
            if '==' in line:
                name, version = line.split('==', 1)
                packages[name.lower()] = version
        return packages
    except subprocess.CalledProcessError as e:
        print(f"Error getting installed packages: {e}")
        return {}

def extract_imports_from_file(file_path: Path) -> Set[str]:
    """Extract import statements from a Python file."""
    imports = set()
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse the AST to extract imports
        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.add(alias.name.split('.')[0])
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.add(node.module.split('.')[0])
        except SyntaxError:
            # If AST parsing fails, use regex as fallback
            import_pattern = r'^(?:from\s+(\w+)|import\s+(\w+))'
            for line in content.split('\n'):
                match = re.match(import_pattern, line.strip())
                if match:
                    module = match.group(1) or match.group(2)
                    if module:
                        imports.add(module.split('.')[0])
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
    
    return imports

def get_all_imports(directory: Path) -> Set[str]:
    """Get all imports from Python files in the directory."""
    all_imports = set()
    
    for py_file in directory.rglob('*.py'):
        # Skip __pycache__ and other non-source directories
        if '__pycache__' in str(py_file) or '.git' in str(py_file):
            continue
        
        file_imports = extract_imports_from_file(py_file)
        all_imports.update(file_imports)
    
    return all_imports

def map_import_to_package(import_name: str) -> str:
    """Map import names to actual package names."""
    # Common mappings where import name differs from package name
    mappings = {
        'cv2': 'opencv-python',
        'PIL': 'Pillow',
        'yaml': 'PyYAML',
        'dateutil': 'python-dateutil',
        'jwt': 'PyJWT',
        'jose': 'python-jose',
        'bson': 'pymongo',
        'gridfs': 'pymongo',
        'motor': 'motor',
        'fastapi': 'fastapi',
        'uvicorn': 'uvicorn',
        'pydantic': 'pydantic',
        'starlette': 'starlette',
        'jinja2': 'Jinja2',
        'bs4': 'beautifulsoup4',
        'lxml': 'lxml',
        'pandas': 'pandas',
        'numpy': 'numpy',
        'requests': 'requests',
        'httpx': 'httpx',
        'aiofiles': 'aiofiles',
        'passlib': 'passlib',
        'argon2': 'argon2-cffi',
        'cryptography': 'cryptography',
        'dotenv': 'python-dotenv',
        'email_validator': 'email-validator',
        'multipart': 'python-multipart',
        'docx': 'python-docx',
        'xlsxwriter': 'xlsxwriter',
        'aiosmtplib': 'aiosmtplib',
        'redis': 'redis',
        'aioredis': 'aioredis',
        'bleach': 'bleach',
        'html_sanitizer': 'html-sanitizer',
        'itsdangerous': 'itsdangerous',
        'regex': 'regex',
        'dnspython': 'dnspython'
    }
    
    return mappings.get(import_name, import_name)

def generate_requirements():
    """Generate requirements.txt based on actual usage."""
    backend_dir = Path(__file__).parent
    
    print("Analyzing Python files for imports...")
    all_imports = get_all_imports(backend_dir)
    
    print("Getting installed packages...")
    installed_packages = get_installed_packages()
    
    print("Mapping imports to packages...")
    used_packages = set()
    
    for import_name in all_imports:
        package_name = map_import_to_package(import_name)
        used_packages.add(package_name.lower())
    
    # Add some packages that are always needed but might not be directly imported
    essential_packages = {
        'fastapi', 'uvicorn', 'gunicorn', 'motor', 'pymongo', 'pydantic',
        'python-jose', 'passlib', 'python-multipart', 'python-dotenv',
        'cryptography', 'argon2-cffi', 'email-validator', 'jinja2',
        'aiofiles', 'beautifulsoup4', 'lxml', 'httpx', 'python-dateutil',
        'pandas', 'regex', 'python-docx', 'xlsxwriter', 'aiosmtplib',
        'redis', 'aioredis', 'bleach', 'html-sanitizer', 'dnspython',
        'starlette'
    }
    
    used_packages.update(essential_packages)
    
    # Filter to only packages that are actually installed
    final_requirements = []
    
    for package in sorted(used_packages):
        if package in installed_packages:
            version = installed_packages[package]
            final_requirements.append(f"{package}=={version}")
        else:
            # Check if it's a different case or has hyphens/underscores
            found = False
            for installed_pkg in installed_packages:
                if (installed_pkg.replace('-', '_').replace('_', '-').lower() == 
                    package.replace('-', '_').replace('_', '-').lower()):
                    version = installed_packages[installed_pkg]
                    final_requirements.append(f"{installed_pkg}=={version}")
                    found = True
                    break
            
            if not found:
                print(f"Warning: Package '{package}' not found in installed packages")
    
    # Write the requirements file
    requirements_file = backend_dir / 'requirements_actual.txt'
    with open(requirements_file, 'w') as f:
        f.write("# Generated requirements based on actual usage\n")
        f.write("# Generated by generate_requirements.py\n\n")
        
        # Group by category
        web_frameworks = []
        databases = []
        security = []
        utilities = []
        email = []
        data_processing = []
        other = []
        
        for req in final_requirements:
            package_name = req.split('==')[0].lower()
            if any(x in package_name for x in ['fastapi', 'uvicorn', 'gunicorn', 'starlette']):
                web_frameworks.append(req)
            elif any(x in package_name for x in ['motor', 'pymongo', 'redis', 'aioredis', 'dnspython']):
                databases.append(req)
            elif any(x in package_name for x in ['jose', 'passlib', 'argon2', 'cryptography', 'bleach', 'html-sanitizer']):
                security.append(req)
            elif any(x in package_name for x in ['email', 'aiosmtplib']):
                email.append(req)
            elif any(x in package_name for x in ['pandas', 'beautifulsoup4', 'lxml', 'docx', 'xlsxwriter']):
                data_processing.append(req)
            elif any(x in package_name for x in ['pydantic', 'dotenv', 'aiofiles', 'httpx', 'dateutil', 'jinja2', 'multipart', 'regex']):
                utilities.append(req)
            else:
                other.append(req)
        
        if web_frameworks:
            f.write("# Web Framework\n")
            f.write('\n'.join(sorted(web_frameworks)) + '\n\n')
        
        if databases:
            f.write("# Database\n")
            f.write('\n'.join(sorted(databases)) + '\n\n')
        
        if security:
            f.write("# Security\n")
            f.write('\n'.join(sorted(security)) + '\n\n')
        
        if utilities:
            f.write("# Utilities\n")
            f.write('\n'.join(sorted(utilities)) + '\n\n')
        
        if data_processing:
            f.write("# Data Processing\n")
            f.write('\n'.join(sorted(data_processing)) + '\n\n')
        
        if email:
            f.write("# Email\n")
            f.write('\n'.join(sorted(email)) + '\n\n')
        
        if other:
            f.write("# Other\n")
            f.write('\n'.join(sorted(other)) + '\n\n')
    
    print(f"Generated {requirements_file}")
    print(f"Found {len(final_requirements)} packages in use")
    
    # Also print summary
    print("\nSummary of packages found:")
    for req in sorted(final_requirements):
        print(f"  {req}")

if __name__ == "__main__":
    generate_requirements()
