from fastapi import APIRouter, HTTPException, Depends, UploadFile, File, Query
from fastapi.responses import JSONResponse
from typing import Optional
import logging
import math
import json
from datetime import datetime, timezone
from models import Complaint
from services.complaint_service import (
    add_complaint,
    get_complaint_by_id,
    update_complaint,
    delete_complaint,
    process_complaint_file,
    get_complaint_graph_data,
)
from services.encryption_service import decrypt_complaint
from routes.auth import get_current_user
from middleware.paid_access_middleware import check_paid_access
from database import get_db
from motor.motor_asyncio import AsyncIOMotorClient
from utils.auth_utils import verify_resource_ownership
from utils.html_sanitizer import sanitize_document_fields
from utils.input_validator import validate_string, validate_dict
from utils.db_sanitizer import sanitize_update_query

logger = logging.getLogger(__name__)

router = APIRouter()

# ✅ Fetch complaints (only for the logged-in user) with pagination
@router.get("/")
async def fetch_complaints(
    complaint_id: Optional[str] = None,
    page: int = Query(1, ge=1, description="Page number, starting from 1"),
    limit: int = Query(10, ge=1, le=100, description="Number of items per page"),
    user: dict = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    try:
        # If complaint_id is provided, get a specific complaint
        if complaint_id:
            logger.info(f"Getting complaint with ID: {complaint_id} for user: {user['email']}")
            user_id = str(user.get("_id", ""))

            # Verify resource ownership using the utility function
            complaint = await verify_resource_ownership(
                resource_id=complaint_id,
                user_id=user_id,
                collection_name="complaints",
                db=db
            )

            logger.info(f"Successfully retrieved complaint: {complaint_id}")
            # Return a single complaint with pagination metadata
            return {
                "data": [complaint],
                "pagination": {
                    "page": 1,
                    "limit": 1,
                    "total": 1,
                    "pages": 1
                }
            }

        # Otherwise, get paginated complaints for the user
        user_id = str(user.get("_id", ""))

        # Calculate skip value for pagination
        skip = (page - 1) * limit

        # Get total count for pagination
        total_complaints = await db.complaints.count_documents({"user_id": user_id})

        # Calculate total pages
        total_pages = math.ceil(total_complaints / limit) if total_complaints > 0 else 1

        logger.info(f"Fetching complaints for user: {user['email']}, page: {page}, limit: {limit}")

        # Get paginated complaints
        complaints_cursor = db.complaints.find({"user_id": user_id}).sort("created_at", -1).skip(skip).limit(limit)
        complaints = await complaints_cursor.to_list(length=limit)

        # Process each complaint - use get_complaint_by_id for consistent data structure
        processed_complaints = []

        for complaint in complaints:
            # Get the complaint ID
            complaint_id = str(complaint["_id"]) if "_id" in complaint else None

            if complaint_id:
                try:
                    # Use get_complaint_by_id to get consistent data structure
                    processed_complaint = await get_complaint_by_id(complaint_id, db)
                    processed_complaints.append(processed_complaint)
                except Exception as e:
                    logger.error(f"Error processing complaint {complaint_id}: {e}")
                    # If get_complaint_by_id fails, fall back to basic processing

                    # Convert ObjectId to string
                    if "_id" in complaint and not isinstance(complaint["_id"], str):
                        complaint["_id"] = str(complaint["_id"])

                    # Handle bank_notice_data if it's a string
                    if 'bank_notice_data' in complaint and isinstance(complaint['bank_notice_data'], str):
                        try:
                            complaint['bank_notice_data'] = json.loads(complaint['bank_notice_data'])
                        except Exception as e:
                            logger.error(f"Failed to convert bank_notice_data string to dictionary: {e}")
                            complaint['bank_notice_data'] = {}

                    # Decrypt sensitive fields
                    decrypted_complaint = decrypt_complaint(complaint)
                    processed_complaints.append(decrypted_complaint)
            else:
                logger.warning(f"Complaint without ID found, skipping")
                continue

        logger.info(f"Successfully fetched and decrypted {len(processed_complaints)} complaints for user: {user['email']} (page {page} of {total_pages})")

        # Return complaints with pagination metadata
        return {
            "data": processed_complaints,
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_complaints,
                "pages": total_pages
            }
        }
    except Exception as e:
        logger.error(f"Error in fetch_complaints: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to fetch complaints: {str(e)}"
        )

# ✅ Add a new complaint (linked to user)
@router.post("/", response_model=dict)
async def create_complaint(complaint: Complaint, user: dict = Depends(get_current_user), db: AsyncIOMotorClient = Depends(get_db)):
    try:
        user_id = str(user.get("_id", ""))
        complaint_id = await add_complaint(user_id, complaint, db)
        return {"message": "Complaint added", "complaint_id": complaint_id}
    except Exception as e:
        logger.error(f"Error creating complaint: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Failed to create complaint: {str(e)}")

# ✅ Update a complaint (Only if it belongs to the user)
@router.put("/{complaint_id}")
async def modify_complaint(complaint_id: str, update_data: dict, user: dict = Depends(get_current_user), db: AsyncIOMotorClient = Depends(get_db)):
    try:
        # Validate complaint_id
        is_valid, sanitized_id, error = validate_string(complaint_id, min_length=1, max_length=50)
        if not is_valid:
            error_msg = error or "Invalid ID"
            raise HTTPException(
                status_code=422,
                detail={
                    "success": False,
                    "error": {
                        "message": f"Invalid complaint ID: {error_msg}",
                        "error_type": "validation_error",
                        "status_code": 422,
                        "field_errors": {"complaint_id": error_msg}
                    }
                }
            )

        # Use the original ID if validation fails but doesn't raise an exception
        complaint_id_to_use = sanitized_id if sanitized_id is not None else complaint_id

        # Validate update_data
        is_valid, sanitized_data, field_errors = validate_dict(
            update_data,
            required_fields=["complaint_number"]
        )
        if not is_valid:
            # Convert any None values to strings for the error message
            error_dict = {k: v or "Invalid value" for k, v in field_errors.items()}
            raise HTTPException(
                status_code=422,
                detail={
                    "success": False,
                    "error": {
                        "message": "Invalid update data",
                        "error_type": "validation_error",
                        "status_code": 422,
                        "field_errors": error_dict
                    }
                }
            )

        # Sanitize any HTML content in the update data
        sanitized_data = sanitize_document_fields(
            sanitized_data,
            fields_to_sanitize=["notes", "summary", "description"],
            is_html=True
        )

        # Sanitize any text content in the update data
        sanitized_data = sanitize_document_fields(
            sanitized_data,
            fields_to_sanitize=["complaint_number", "complainant_name", "category", "subcategory", "status"],
            is_html=False
        )

        # Sanitize the update query to prevent NoSQL injection
        sanitized_data = sanitize_update_query(sanitized_data)

        user_id = str(user.get("_id", ""))

        # Verify resource ownership using the utility function
        await verify_resource_ownership(
            resource_id=complaint_id_to_use,
            user_id=user_id,
            collection_name="complaints",
            db=db
        )

        success = await update_complaint(complaint_id_to_use, user_id, sanitized_data, db)
        if not success:
            raise HTTPException(
                status_code=404,
                detail={
                    "success": False,
                    "error": {
                        "message": "Complaint not found or no changes made",
                        "error_type": "not_found_error",
                        "status_code": 404
                    }
                }
            )

        return {"message": "Complaint updated successfully"}
    except HTTPException as e:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error updating complaint: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": {
                    "message": "An error occurred while updating the complaint",
                    "error_type": "server_error",
                    "status_code": 500
                }
            }
        )

# ✅ Delete a complaint (Only if it belongs to the user)
@router.delete("/{complaint_id}")
async def remove_complaint(complaint_id: str, user: dict = Depends(get_current_user), db: AsyncIOMotorClient = Depends(get_db)):
    user_id = str(user.get("_id", ""))

    # Verify resource ownership using the utility function
    await verify_resource_ownership(
        resource_id=complaint_id,
        user_id=user_id,
        collection_name="complaints",
        db=db
    )

    success = await delete_complaint(complaint_id, user_id, db)
    if not success:
        raise HTTPException(status_code=404, detail="Complaint not found or already deleted")

    return {"message": "Complaint deleted successfully"}

# The functionality of this endpoint has been merged into the main fetch_complaints endpoint
# to avoid routing conflicts

# ✅ Get graph visualization data for a specific complaint
@router.get("/graph/{complaint_id}", dependencies=[Depends(check_paid_access)])
async def get_graph_data(complaint_id: str, user: dict = Depends(get_current_user), db: AsyncIOMotorClient = Depends(get_db)):
    """Get graph visualization data for a specific complaint.

    This endpoint returns only the data needed for graph visualization,
    including metadata, graph data, and bank notice data.
    """
    try:
        user_id = str(user.get("_id", ""))
        logger.info(f"Getting graph data for complaint: {complaint_id} for user: {user['email']}")

        # Verify resource ownership using the utility function
        await verify_resource_ownership(
            resource_id=complaint_id,
            user_id=user_id,
            collection_name="complaints",
            db=db
        )

        result = await get_complaint_graph_data(complaint_id, user_id, db)
        logger.info(f"Successfully retrieved graph data for complaint: {complaint_id}")
        return result
    except Exception as e:
        logger.error(f"Error in get_graph_data: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get graph data: {str(e)}"
        )

# ✅ Get only graph data for a specific complaint (optimized endpoint)
@router.get("/graph-data/{complaint_id}", dependencies=[Depends(check_paid_access)])
async def get_optimized_graph_data(
    complaint_id: str,
    fields: str = Query("graph_data,metadata", description="Comma-separated list of fields to include"),
    user: dict = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """Get only the graph data for a specific complaint.

    This optimized endpoint returns only the specific fields requested,
    reducing data transfer and processing time.
    """
    try:
        user_id = str(user.get("_id", ""))
        logger.info(f"Getting optimized graph data for complaint: {complaint_id} for user: {user['email']}")
        logger.info(f"Requested fields: {fields}")

        # Verify resource ownership using the utility function
        await verify_resource_ownership(
            resource_id=complaint_id,
            user_id=user_id,
            collection_name="complaints",
            db=db
        )

        # Parse requested fields
        field_list = [f.strip() for f in fields.split(",")]

        try:
            # Get full graph data - await the async function
            full_data = await get_complaint_graph_data(complaint_id, user_id, db)

            if not full_data:
                logger.error(f"No graph data found for complaint {complaint_id}")
                raise HTTPException(status_code=404, detail="Graph data not found")

            # Filter to only requested fields
            result = {}
            for field in field_list:
                if field in full_data:
                    result[field] = full_data[field]
                else:
                    logger.warning(f"Requested field {field} not found in graph data")

            logger.info(f"Successfully retrieved optimized graph data for complaint: {complaint_id}")
            return result
        except Exception as e:
            logger.error(f"Error getting graph data for complaint {complaint_id}: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail=f"Error getting graph data: {str(e)}"
            )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error in get_optimized_graph_data: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get optimized graph data: {str(e)}"
        )

# ✅ Get only CSV data for a specific complaint (optimized endpoint)
@router.get("/csv-data/{complaint_id}", dependencies=[Depends(check_paid_access)])
async def get_optimized_csv_data(
    complaint_id: str,
    fields: str = Query("csv_data_base64", description="Comma-separated list of fields to include"),
    user: dict = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """Get only the CSV data for a specific complaint.

    This endpoint is optimized to return only the CSV data for a complaint,
    which is more efficient than fetching the entire complaint object.
    """
    logger.info(f"get_optimized_csv_data called for complaint_id: {complaint_id}, fields: {fields}")
    try:
        user_id = str(user.get("_id", ""))
        logger.info(f"Getting optimized CSV data for complaint: {complaint_id} for user: {user['email']}")
        logger.info(f"Requested fields: {fields}")

        # Verify resource ownership using the utility function
        await verify_resource_ownership(
            resource_id=complaint_id,
            user_id=user_id,
            collection_name="complaints",
            db=db
        )

        # Parse requested fields
        field_list = [f.strip() for f in fields.split(",")]

        # Get the complaint directly from the database
        complaint = await get_complaint_by_id(complaint_id, db)

        # Filter to only requested fields
        result = {}
        for field in field_list:
            if field in complaint:
                result[field] = complaint[field]
                logger.info(f"Field '{field}' found in complaint data")

                # Log details about the field
                if field == 'csv_data_base64':
                    if complaint[field]:
                        logger.info(f"csv_data_base64 type: {type(complaint[field])}")
                        logger.info(f"csv_data_base64 length: {len(str(complaint[field]))}")
                        # Log a sample of the data
                        sample = str(complaint[field])[:50] + '...' if len(str(complaint[field])) > 50 else str(complaint[field])
                        logger.info(f"csv_data_base64 sample: {sample}")
                    else:
                        logger.warning(f"csv_data_base64 is empty or None")
            else:
                logger.warning(f"Requested field '{field}' not found in complaint data")

        # Special handling for csv_data_base64 if it's missing but we have transactions
        if 'csv_data_base64' in field_list and 'csv_data_base64' not in result:
            if 'transactions' in complaint and complaint['transactions']:
                logger.info(f"csv_data_base64 not found, generating from {len(complaint['transactions'])} transactions")
                from services.csv_service import generate_csv_from_transactions, csv_to_base64

                # Generate CSV from transactions
                csv_data = generate_csv_from_transactions(complaint['transactions'])

                # Convert to base64
                base64_data = csv_to_base64(csv_data.getvalue())

                # Add to result
                result['csv_data_base64'] = base64_data
                logger.info(f"Generated csv_data_base64 from transactions (length: {len(base64_data)})")
            else:
                logger.warning("csv_data_base64 requested but not found, and no transactions available")

        # Log the result
        logger.info(f"CSV data fetched for complaint {complaint_id}")
        logger.info(f"Result keys: {list(result.keys())}")
        logger.info(f"Has csv_data_base64: {'csv_data_base64' in result}")
        if 'csv_data_base64' in result:
            logger.info(f"csv_data_base64 type: {type(result['csv_data_base64'])}")
        logger.info(f"Successfully retrieved optimized CSV data for complaint: {complaint_id}")
        return result
    except Exception as e:
        logger.error(f"Error in get_optimized_csv_data: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get optimized CSV data: {str(e)}"
        )

# ✅ Get only notice data for a specific complaint (optimized endpoint)
@router.get("/notice-data/{complaint_id}", dependencies=[Depends(check_paid_access)])
async def get_optimized_notice_data(
    complaint_id: str,
    fields: str = Query("bank_notice_data,metadata", description="Comma-separated list of fields to include"),
    user: dict = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """Get only the notice data for a specific complaint.

    This optimized endpoint returns only the notice data and metadata,
    reducing data transfer and processing time.
    """
    try:
        user_id = str(user.get("_id", ""))
        logger.info(f"Getting optimized notice data for complaint: {complaint_id} for user: {user['email']}")
        logger.info(f"Requested fields: {fields}")

        # Verify resource ownership using the utility function
        await verify_resource_ownership(
            resource_id=complaint_id,
            user_id=user_id,
            collection_name="complaints",
            db=db
        )

        # Parse requested fields
        field_list = [f.strip() for f in fields.split(",")]

        # Get the complaint directly from the database
        complaint = await get_complaint_by_id(complaint_id, db)

        # Filter to only requested fields
        result = {}
        for field in field_list:
            if field in complaint:
                result[field] = complaint[field]
                logger.info(f"Field '{field}' found in complaint data")
            else:
                logger.warning(f"Requested field '{field}' not found in complaint data")

        # Log the result
        logger.info(f"Notice data fetched for complaint {complaint_id}")
        logger.info(f"Result keys: {list(result.keys())}")
        logger.info(f"Has bank_notice_data: {'bank_notice_data' in result}")
        if 'bank_notice_data' in result:
            logger.info(f"bank_notice_data type: {type(result['bank_notice_data'])}")
            if isinstance(result['bank_notice_data'], dict):
                logger.info(f"bank_notice_data keys: {list(result['bank_notice_data'].keys())}")
            elif isinstance(result['bank_notice_data'], list):
                logger.info(f"bank_notice_data is a list with {len(result['bank_notice_data'])} items")
                if result['bank_notice_data'] and len(result['bank_notice_data']) > 0:
                    logger.info(f"First item type: {type(result['bank_notice_data'][0])}")
                    if isinstance(result['bank_notice_data'][0], dict):
                        logger.info(f"First item keys: {list(result['bank_notice_data'][0].keys())}")

        logger.info(f"Successfully retrieved optimized notice data for complaint: {complaint_id}")
        return result
    except Exception as e:
        logger.error(f"Error in get_optimized_notice_data: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get optimized notice data: {str(e)}"
        )

# ✅ Action endpoint for complaints (delete, etc.)
@router.post("/action")
async def complaint_action(action_data: dict, user: dict = Depends(get_current_user), db: AsyncIOMotorClient = Depends(get_db)):
    user_id = str(user.get("_id", ""))
    action = action_data.get("action")
    complaint_number = action_data.get("complaint_number")
    complaint_id = action_data.get("complaint_id")

    if not action or (not complaint_number and not complaint_id):
        logger.warning("Missing required fields")
        raise HTTPException(status_code=400, detail="Missing required fields")

    # Find the complaint by complaint_number or complaint_id
    complaint = None
    identifier = complaint_id or complaint_number

    if not identifier:
        logger.warning("No identifier provided")
        raise HTTPException(status_code=400, detail="Missing complaint identifier")

    logger.info(f"Looking for complaint with identifier: {identifier}")

    # Verify resource ownership using the utility function
    try:
        complaint = await verify_resource_ownership(
            resource_id=str(identifier),
            user_id=user_id,
            collection_name="complaints",
            db=db
        )
        logger.info(f"Found complaint: {complaint.get('complaint_number')}")
    except HTTPException as e:
        logger.error(f"Error finding complaint or unauthorized access: {e}")
        raise

    if action == "delete":
        # Delete the complaint
        complaint_id = str(complaint["_id"])
        logger.info(f"Deleting complaint with ID: {complaint_id}")
        try:
            await delete_complaint(complaint_id, user_id, db)
            # If we get here, the deletion was successful
            logger.info(f"Successfully deleted complaint: {complaint_id}")
            return {"message": "Complaint deleted successfully"}
        except HTTPException as e:
            logger.warning(f"Failed to delete complaint: {complaint_id} - {e.detail}")
            raise HTTPException(status_code=404, detail="Complaint not found or already deleted")
    elif action == "update":
        # Update the complaint
        complaint_id = str(complaint["_id"])
        update_data = action_data.get("update_data")

        if not update_data:
            logger.warning("Missing update_data field in update request")
            raise HTTPException(status_code=400, detail="Missing update_data field")

        logger.info(f"Updating complaint with ID: {complaint_id}")
        success = await update_complaint(complaint_id, user_id, update_data, db)

        if not success:
            logger.warning(f"Failed to update complaint: {complaint_id}")
            raise HTTPException(status_code=404, detail="Complaint not found or no changes made")

        logger.info(f"Successfully updated complaint: {complaint_id}")
        return {"message": "Complaint updated successfully"}
    elif action == "update_csv":
        # Update CSV data for the complaint
        complaint_id = str(complaint["_id"])
        # Check for both field names for backward compatibility
        csv_data = action_data.get("csv_data") or action_data.get("csv_data_base64")
        metadata = action_data.get("metadata", {})
        # Check for row operation details
        row_operation = action_data.get("row_operation", {})

        logger.info(f"Received metadata in update_csv request: {metadata}")
        logger.info(f"Metadata type: {type(metadata)}")
        if metadata:
            logger.info(f"Metadata keys: {list(metadata.keys()) if isinstance(metadata, dict) else 'Not a dict'}")

        if row_operation:
            logger.info(f"Row operation details included: {row_operation}")

        if not csv_data:
            logger.warning("Missing csv_data or csv_data_base64 field in update_csv request")
            raise HTTPException(status_code=400, detail="Missing CSV data field")

        logger.info(f"Updating CSV data for complaint with ID: {complaint_id}")

        try:
            # Prepare update data
            import base64
            import re

            # Check if the CSV data is already base64 encoded
            is_already_base64 = False
            try:
                # Check if it looks like base64 (only contains base64 characters)
                if re.match(r'^[A-Za-z0-9+/=]+$', csv_data.strip()):
                    try:
                        # Try to decode it to see if it's valid base64
                        decoded_test = base64.b64decode(csv_data)
                        # If decoding succeeds and the result looks like CSV, it's already base64
                        decoded_str = decoded_test.decode('utf-8')
                        if ',' in decoded_str and ('\n' in decoded_str or '\r' in decoded_str):
                            is_already_base64 = True
                            logger.info("CSV data is already base64 encoded")
                        else:
                            logger.info("Data looks like base64 but doesn't decode to CSV format")
                    except Exception:
                        # If decoding fails, it's not base64
                        logger.info("CSV data is not base64 encoded")
            except Exception as e:
                logger.warning(f"Error checking if CSV data is base64: {str(e)}")

            # Encode CSV data as base64 to store in the database (only if not already encoded)
            if is_already_base64:
                csv_data_base64 = csv_data  # Use as-is if already base64
                logger.info("Using CSV data as-is (already base64 encoded)")
            else:
                csv_data_base64 = base64.b64encode(csv_data.encode('utf-8')).decode('utf-8')
                logger.info("Encoded CSV data to base64 for storage")

            # Create update data object
            update_data = {
                "csv_data_base64": csv_data_base64,  # Keep using csv_data_base64 for database storage
                "last_modified": datetime.now(timezone.utc).isoformat()
            }

            # Always preserve and update metadata
            # First, get existing metadata from the complaint
            existing_metadata = {}
            if "metadata" in complaint and isinstance(complaint["metadata"], dict):
                # Deep copy existing metadata to avoid reference issues
                existing_metadata = dict(complaint["metadata"])
                logger.info(f"Found existing metadata with keys: {', '.join(existing_metadata.keys())}")
            elif "metadata" in complaint and isinstance(complaint["metadata"], str):
                # If metadata is stored as JSON string, parse it
                try:
                    import json
                    existing_metadata = json.loads(complaint["metadata"])
                    logger.info(f"Parsed existing metadata from JSON string with keys: {', '.join(existing_metadata.keys())}")
                except Exception as e:
                    logger.warning(f"Failed to parse metadata JSON string: {str(e)}")
                    existing_metadata = {}
            else:
                logger.info("No existing metadata found in complaint")

            # If new metadata is provided, filter and update only essential metadata
            if metadata and isinstance(metadata, dict):
                logger.info(f"Processing metadata with keys: {', '.join(metadata.keys())}")

                # Define essential metadata fields that should be preserved
                # Include all complainant details and address information
                essential_fields = {
                    # Basic complaint info
                    'complaint_number', 'complainant_name', 'amount', 'category',
                    'subcategory', 'date', 'status', 'total_amount', 'total_hold',
                    'total_withdrawal', 'fraud_type',
                    # Complainant details
                    'complainant_mobile', 'complainant_email', 'complainant_relationship',
                    'complainant_gender', 'complainant_phone',
                    # Address information (as object)
                    'address',
                    # Individual address fields (for backward compatibility)
                    'house_no', 'street', 'colony', 'village_town_city', 'tehsil',
                    'district', 'state', 'country', 'police_station', 'pin_code',
                    # Financial details
                    'lien_amount', 'total_fraudulent_amount',
                    # Additional metadata
                    'original_complaint_date', 'extracted_text'
                }

                # Extract only essential metadata from the provided metadata
                # Look for complaintMetadata first, then fall back to root level
                complaint_metadata = metadata.get('complaintMetadata', {})
                if complaint_metadata and isinstance(complaint_metadata, dict):
                    logger.info(f"Found complaintMetadata with keys: {', '.join(complaint_metadata.keys())}")
                    for key, value in complaint_metadata.items():
                        if key in essential_fields:
                            existing_metadata[key] = value
                            logger.info(f"Preserved essential field: {key}")

                # Also check root level for essential fields
                for key, value in metadata.items():
                    if key in essential_fields:
                        existing_metadata[key] = value
                        logger.info(f"Preserved essential field from root: {key}")

                # Handle complaintSummary if provided
                complaint_summary = metadata.get('complaintSummary', {})
                if complaint_summary and isinstance(complaint_summary, dict):
                    logger.info(f"Found complaintSummary with keys: {', '.join(complaint_summary.keys())}")
                    # Store the summary to be preserved
                    update_data["summary"] = complaint_summary

                logger.info(f"Filtered metadata to essential fields only: {', '.join(existing_metadata.keys())}")
            else:
                logger.info("No new metadata provided in request")

            # Ensure we preserve important metadata fields
            # If we have transaction summary data in the metadata, make sure it's preserved
            if "total_hold" in existing_metadata or "total_withdrawal" in existing_metadata:
                logger.info(f"Preserving transaction summary data in metadata")

                # If the new metadata contains updated transaction summary, use those values
                if metadata and isinstance(metadata, dict):
                    if "total_hold" in metadata:
                        existing_metadata["total_hold"] = metadata["total_hold"]
                    if "total_withdrawal" in metadata:
                        existing_metadata["total_withdrawal"] = metadata["total_withdrawal"]

            # Store metadata as a dictionary object (not JSON string)
            update_data["metadata"] = existing_metadata

            # Also preserve the summary data (contains complainant details like mobile, email, address)
            if "summary" in complaint and isinstance(complaint["summary"], dict):
                existing_summary = dict(complaint["summary"])
                update_data["summary"] = existing_summary
                logger.info(f"Preserved summary data with keys: {', '.join(existing_summary.keys())}")
            else:
                logger.info("No summary data found to preserve")

            logger.info(f"Metadata fields being preserved: {', '.join(existing_metadata.keys())}")

            # Update the complaint with CSV data
            success = await update_complaint(complaint_id, user_id, update_data, db)

            if not success:
                logger.warning(f"Failed to update CSV data for complaint: {complaint_id}")
                raise HTTPException(status_code=404, detail="Complaint not found or no changes made")

            # Now regenerate the graph data from the updated CSV
            logger.info(f"Regenerating graph data from updated CSV for complaint: {complaint_id}")

            try:
                # Import necessary functions
                from services.graph_service import process_csv_data

                # Process the CSV data to extract transactions and generate graph data
                transactions, graph_data = process_csv_data(csv_data, existing_metadata)

                if transactions and graph_data:
                    # Create a new update with the graph data
                    graph_update = {
                        "graph_data": graph_data,
                        "transactions": transactions,  # Store the transactions separately for easier access
                        "last_modified": datetime.now(timezone.utc).isoformat()
                    }

                    # Update the complaint with the new graph data
                    graph_success = await update_complaint(complaint_id, user_id, graph_update, db)

                    if graph_success:
                        logger.info(f"Successfully updated graph data for complaint: {complaint_id}")

                        # Log transaction counts for debugging
                        logger.info(f"Updated with {len(transactions)} transactions in graph data")

                        # Count transactions by type for verification
                        txn_types = {}
                        for txn in transactions:
                            txn_type = txn.get('txn_type', txn.get('type', 'unknown'))
                            if txn_type not in txn_types:
                                txn_types[txn_type] = 0
                            txn_types[txn_type] += 1

                        logger.info(f"Transaction types: {txn_types}")
                    else:
                        logger.warning(f"Failed to update graph data for complaint: {complaint_id}")
                else:
                    logger.warning(f"No transactions or graph data generated from CSV for complaint: {complaint_id}")
            except Exception as graph_error:
                # Log the error but don't fail the whole operation
                logger.error(f"Error regenerating graph data: {str(graph_error)}", exc_info=True)
                # Continue with the operation - at least the CSV data was updated

            logger.info(f"Successfully updated CSV data for complaint: {complaint_id}")
            # Return the updated complaint object
            updated_complaint = await get_complaint_by_id(complaint_id, db)
            return updated_complaint
        except Exception as e:
            logger.error(f"Error updating CSV data: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Failed to update CSV data: {str(e)}")
    else:
        logger.warning(f"Invalid action: {action}")
        raise HTTPException(status_code=400, detail="Invalid action")

@router.post("/upload", response_model=dict, dependencies=[Depends(check_paid_access)])
async def upload_complaint_file(
    file: UploadFile = File(...),
    user: dict = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Upload a complaint file (HTML only) and process it

    Args:
        file: The uploaded HTML file
        user: Current authenticated user
        db: Database connection

    Returns:
        JSON response with complaint ID and status
    """
    try:
        # Validate file existence
        if not file or not file.filename:
            raise HTTPException(
                status_code=422,
                detail={
                    "success": False,
                    "error": {
                        "message": "No file provided or filename is missing",
                        "error_type": "validation_error",
                        "status_code": 422,
                        "field_errors": {"file": "File is required"}
                    }
                }
            )

        # Validate file type with strict checking
        filename = file.filename.lower() if file.filename else ""
        content_type = file.content_type or ""

        # Check file extension
        valid_extensions = ['.html', '.htm']
        if not any(filename.endswith(ext) for ext in valid_extensions):
            raise HTTPException(
                status_code=422,
                detail={
                    "success": False,
                    "error": {
                        "message": "Invalid file type",
                        "error_type": "validation_error",
                        "status_code": 422,
                        "field_errors": {"file": "Only HTML files are allowed"}
                    }
                }
            )

        # Check content type
        valid_content_types = ['text/html', 'application/xhtml+xml']
        if content_type and not any(content_type.startswith(ct) for ct in valid_content_types):
            logger.warning(f"Suspicious content type for HTML file: {content_type}")
            # We'll still process it but log the warning

        # Read file content with size limit
        try:
            file_content = await file.read()
            file_size = len(file_content)
        except Exception as e:
            logger.error(f"Error reading file: {str(e)}")
            raise HTTPException(
                status_code=422,
                detail={
                    "success": False,
                    "error": {
                        "message": "Error reading file",
                        "error_type": "validation_error",
                        "status_code": 422,
                        "field_errors": {"file": "Could not read file content"}
                    }
                }
            )

        # Check file size (10MB limit)
        max_size = 10 * 1024 * 1024  # 10MB
        if file_size > max_size:
            raise HTTPException(
                status_code=422,
                detail={
                    "success": False,
                    "error": {
                        "message": f"File size exceeds the {max_size // (1024 * 1024)}MB limit",
                        "error_type": "validation_error",
                        "status_code": 422,
                        "field_errors": {"file": f"File must be smaller than {max_size // (1024 * 1024)}MB"}
                    }
                }
            )

        # Check if file content appears to be HTML
        if not file_content.strip().startswith(b'<!DOCTYPE') and not file_content.strip().startswith(b'<html'):
            logger.warning(f"File does not appear to be valid HTML: {filename}")
            # We'll still process it but log the warning

        # Reset file position for processing
        await file.seek(0)

        # Process the uploaded file
        user_id = str(user.get("_id", ""))
        complaint_data = await process_complaint_file(
            file=file,
            file_content=file_content,
            title=None,  # No title needed
            description=None,  # No description needed
            user_id=user_id,
            db=db
        )

        # Sanitize complaint data
        complaint_data = sanitize_document_fields(
            complaint_data,
            fields_to_sanitize=["complaint_number", "complainant_name", "category", "subcategory", "status", "title"],
            is_html=False
        )

        # Sanitize HTML fields
        complaint_data = sanitize_document_fields(
            complaint_data,
            fields_to_sanitize=["notes", "summary", "description"],
            is_html=True
        )

        # Create and save complaint
        try:
            complaint = Complaint(**complaint_data)
            complaint_id = await add_complaint(user_id, complaint, db)
            logger.info(f"Complaint uploaded successfully: {complaint_id}")
        except Exception as e:
            logger.error(f"Error adding complaint: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail=f"Failed to add complaint: {str(e)}")
        return JSONResponse(
            status_code=201,
            content={
                "message": "Complaint uploaded successfully",
                "complaint_id": complaint_id
            }
        )

    except HTTPException as he:
        logger.error(f"HTTP error during complaint upload: {str(he)}")
        raise
    except Exception as e:
        logger.error(f"Error uploading complaint: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail={
                "success": False,
                "error": {
                    "message": "An error occurred while processing your complaint",
                    "error_type": "server_error",
                    "status_code": 500
                }
            }
        )
