from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Depends, Response, Request
from typing import Dict, Any, Optional
from pydantic import BaseModel
import os
import tempfile
import shutil
from lxml import html
import re
from io import BytesIO, StringIO
import base64
import logging
import datetime
from routes.auth import get_current_user
from models import User
from database import get_db
from motor.motor_asyncio import AsyncIOMotorClient
from services.graph_service import generate_graph_data
from services.notice_service import generate_notice_with_docxtpl, structure_transactions_by_bank
from services.csv_service import generate_csv_from_transactions, csv_to_base64, organize_transactions_by_relationships
from utils.input_validator import validate_string, validate_numeric
from utils.html_sanitizer import sanitize_html
from utils.db_sanitizer import sanitize_find_query
from utils.error_utils import validation_error, server_error

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter()

def extract_metadata(tree):
    # Try multiple approaches to find metadata
    def get_text_by_id(id_):
        # DEBUG: Log the XPath query being used
        logger.info(f"Extracting element with ID: {id_}")

        # First check if the element exists at all
        element_exists = tree.xpath(f"//*[@id='{id_}']")
        logger.info(f"Element with ID '{id_}' exists: {bool(element_exists)}")

        # Try to get the text content
        el = tree.xpath(f"//*[@id='{id_}']/text()") #extract_metadata
        result = el[0].strip() if el else ""

        # Log all element extractions for debugging
        logger.info(f"Extracting {id_}: Found {len(el)} elements, value: '{result}'")

        return result

    def get_text_by_label(label_text):
        # DEBUG: Log the label search
        logger.info(f"Searching for label with text: '{label_text}'")

        # Check if any labels with this text exist
        labels = tree.xpath(f"//label[contains(text(), '{label_text}')]")
        logger.info(f"Found {len(labels)} labels containing text '{label_text}'")

        # Find label with this text, then get the next element's text
        elements = tree.xpath(f"//label[contains(text(), '{label_text}')]/following::span[1]/text()")
        result = elements[0].strip() if elements else ""

        logger.info(f"Extracted value for label '{label_text}': '{result}'")
        return result

    # Try multiple IDs for each field
    acknowledgement_no = get_text_by_id("txtcrimeId")

    # Ensure acknowledgement_no is numeric
    if acknowledgement_no and not acknowledgement_no.isdigit():
        acknowledgement_no = ""

    category = get_text_by_id("ContentPlaceHolder1_txtcategoryCrime") or get_text_by_id("ContentPlaceHolder1_lblCatCrime")
    subcategory = get_text_by_id("ContentPlaceHolder1_txtcrimesubcat") or get_text_by_id("ContentPlaceHolder1_lblsubcat")
    date = get_text_by_id("ContentPlaceHolder1_lblincidentdatetime") or get_text_by_id("ContentPlaceHolder1_lblComplaintDate") #id="ContentPlaceHolder1_lblComplaintDate"

    # Extract complainant details
    mobile_number = get_text_by_id("ContentPlaceHolder1_lblMNo") or get_text_by_label("Mobile No.")

    # Extract email - use ONLY the ID approach for email extraction
    email_id = get_text_by_id("ContentPlaceHolder1_lblEmailId")
    logger.info(f"Extracted email using ID: {email_id}")

    # Extract relationship - use ONLY the ID approach for relationship extraction
    relationship = get_text_by_id("ContentPlaceHolder1_lblRelation")
    logger.info(f"Extracted relationship using ID: {relationship}")

    gender = get_text_by_id("ContentPlaceHolder1_lblgender") or get_text_by_label("Gender")

    # Extract address details
    house_no = get_text_by_id("ContentPlaceHolder1_lblCHNo") or get_text_by_label("House No.")
    street = get_text_by_id("ContentPlaceHolder1_lblCStreet") or get_text_by_label("Street Name")
    colony = get_text_by_id("ContentPlaceHolder1_lblCColony") or get_text_by_label("Colony")
    village_town_city = get_text_by_id("ContentPlaceHolder1_lblCVill") or get_text_by_label("Village/ Town/ City")
    tehsil = get_text_by_id("ContentPlaceHolder1_lblCTehsil") or get_text_by_label("Tehsil")
    district = get_text_by_id("ContentPlaceHolder1_lblCDist") or get_text_by_label("District")
    state = get_text_by_id("ContentPlaceHolder1_lblCState") or get_text_by_label("State")
    country = get_text_by_id("ContentPlaceHolder1_lblCCount") or get_text_by_label("Country")
    police_station = get_text_by_id("ContentPlaceHolder1_lblCPStation") or get_text_by_label("Police Station")
    pin_code = get_text_by_id("ContentPlaceHolder1_lblCpin") or get_text_by_label("Pin Code")

    # If IDs fail, try finding by label text
    if not acknowledgement_no:
        temp_ack = get_text_by_label("Acknowledgement No")
        if temp_ack and temp_ack.isdigit():
            acknowledgement_no = temp_ack

    if not category:
        category = get_text_by_label("Category of Complaint")
    if not subcategory:
        subcategory = get_text_by_label("Sub Category of Complaint")
    if not date:
        date = get_text_by_label("Complaint Date")

    metadata = {
        "total_amount": get_text_by_id("ContentPlaceHolder1_lbltotalfraudlentamt") or get_text_by_label("Total Fraudulent Amount"),
        "lien_amount": get_text_by_id("ContentPlaceHolder1_lbltotallienAmount") or get_text_by_label("Total Lien Amount"),
        "date": date,
        "complaint_number": acknowledgement_no,
        "category": category,
        "subcategory": subcategory,
        "complainant_name": get_text_by_id("ContentPlaceHolder1_lblName") or get_text_by_label("Name"),
        "complainant_mobile": mobile_number,
        "complainant_email": email_id,
        "complainant_relationship": relationship,
        "complainant_gender": gender,
        "address": {
            "house_no": house_no,
            "street": street,
            "colony": colony,
            "village_town_city": village_town_city,
            "tehsil": tehsil,
            "district": district,
            "state": state,
            "country": country,
            "police_station": police_station,
            "pin_code": pin_code
        }
    }
    return metadata

# Removed extract_complaint_summary function as it's no longer needed
# We now use a simpler approach that only relies on metadata

def clean_text(text):
    """
    Clean text by removing whitespace, newlines, and other unwanted characters.

    Args:
        text: Text to clean

    Returns:
        Cleaned text
    """
    if not text:
        return ""
    return text.strip().replace('\n', ' ').replace('\r', '').replace('\t', ' ').replace('  ', ' ')

def extract_date(text):
    """
    Extract date from text in various formats.

    Args:
        text: Text containing a date

    Returns:
        Extracted date string or empty string if not found
    """
    if not text:
        return ""

    # Try common date formats
    date_patterns = [
        r'\d{2}[/.-]\d{2}[/.-]\d{4}',  # DD/MM/YYYY or DD-MM-YYYY
        r'\d{4}[/.-]\d{2}[/.-]\d{2}',  # YYYY/MM/DD or YYYY-MM-DD
        r'\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{4}',  # 1 Jan 2023
        r'(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2},?\s+\d{4}'   # Jan 1, 2023
    ]

    for pattern in date_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(0)

    return ""

def extract_cheque_no(text):
    """
    Extract cheque number from text.

    Args:
        text: Text containing a cheque number

    Returns:
        Extracted cheque number or empty string if not found
    """
    if not text:
        return ""

    # Try common cheque number patterns
    cheque_patterns = [
        r'cheque\s+no\.?\s*(\d+)',  # Cheque No. 123456
        r'chq\s+no\.?\s*(\d+)',    # Chq No. 123456
        r'cheque\s*(\d+)',         # Cheque 123456
        r'chq\s*(\d+)'             # Chq 123456
    ]

    for pattern in cheque_patterns:
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            return match.group(1)

    return ""

def should_skip_row(remarks):
    """
    Determine if a row should be skipped based on its remarks.

    Args:
        remarks: Remarks text from the row

    Returns:
        True if the row should be skipped, False otherwise
    """
    if not remarks:
        return False

    # Skip rows with these keywords
    skip_keywords = [
        # Duplicate-related keywords
        "duplicate entry",
        "already attended",
        "details already shared",
        "information already provided"
    ]

    remarks_lower = remarks.lower()
    for keyword in skip_keywords:
        if keyword in remarks_lower:
            return True

    return False

def clean_account_number(account_no):
    """Helper function to clean account numbers by removing common prefixes"""
    if not account_no:
        return ""

    # Convert to string and lowercase for case-insensitive replacement
    account_str = str(account_no)
    account_lower = account_str.lower()

    # Check for common prefixes
    prefixes = [
        "a/c no.-:", "a/c no.:", "a/c no.", "a/c",
        "A/C No.-:", "A/C No.:", "A/C No.", "A/C",
        "account no.-:", "account no.:", "account no.", "account"
    ]

    # Replace all prefixes
    for prefix in prefixes:
        if prefix.lower() in account_lower:
            account_str = account_str.replace(prefix, "")

    # Remove standalone "-:" prefix that might appear at the beginning
    if account_str.startswith("-:"):
        account_str = account_str[2:]

    # Also check for other variations of the prefix
    if account_str.startswith("- :"):
        account_str = account_str[3:]
    if account_str.startswith("-: "):
        account_str = account_str[3:]

    return account_str.strip()


def extract_merchant_info(cell):
    """
    Helper function to extract merchant information from a cell.

    Args:
        cell: The HTML cell element to extract merchant info from

    Returns:
        Extracted merchant info or empty string if not found or only placeholders
    """
    if cell is None:
        return ""

    # Look for divs that contain MID or TID information
    merchant_divs = cell.xpath('.//div[contains(text(), "MID-:") or contains(text(), "TID-:")]')

    if not merchant_divs:
        return ""

    # Get the complete text content of the div
    div_text = clean_text(merchant_divs[0].text_content())

    # Check if it contains additional information beyond just the placeholders
    has_mid_info = "MID-:" in div_text and len(div_text.split("MID-:")[1].strip()) > 0
    has_tid_info = "TID-:" in div_text and len(div_text.split("TID-:")[1].strip()) > 0

    if has_mid_info or has_tid_info:
        # Use the entire div content as the merchant info
        return div_text
    else:
        # Only placeholders without additional data, return empty string
        return ""

# Main extraction logic functions for different types of complaints

def extract_banking_upi_transactions(tree):
    """
    Extract Banking/UPI transactions using specific element IDs.
    This function extracts data directly from the HTML elements using their IDs
    rather than from table rows.

    Args:
        tree: HTML tree parsed with lxml

    Returns:
        List of transaction dictionaries with only the specified fields
    """
    logger.info("Extracting Banking/UPI transactions using element IDs")
    transactions = []

    # Find the main transaction table
    main_table = tree.xpath("//table[@id='ContentPlaceHolder1_gedotherbank']")
    if not main_table:
        logger.warning("Main transaction table not found for Banking/UPI extraction")
        return transactions

    # Get all rows from the table (skip header)
    rows = main_table[0].xpath('.//tr')[1:] if main_table else []
    if not rows:
        logger.warning("No rows found in main transaction table for Banking/UPI extraction")
        return transactions

    # Process each row
    for row_idx, row in enumerate(rows):
        try:
            # Initialize transaction object with only the required fields
            transaction = {
                "fraud_type": "banking_upi",
            }

            # Extract data using the specific element IDs
            # 1. Sender bank account - use row-specific XPath
            sender_account_id = f"ContentPlaceHolder1_gedotherbank_lblppprootaccntidmany_{row_idx}"
            sender_account_elements = row.xpath(f".//span[@id='{sender_account_id}']/text()")
            if not sender_account_elements:
                # Fallback to tree-wide search but with exact ID match
                sender_account_elements = tree.xpath(f"//span[@id='{sender_account_id}']/text()")
            transaction["sender_account"] = clean_account_number(clean_text(sender_account_elements[0]) if sender_account_elements else "")

            # 2. Sender transaction ID - use row-specific XPath
            sender_txn_id = f"ContentPlaceHolder1_gedotherbank_lblpproottransidmany_{row_idx}"
            sender_txn_elements = row.xpath(f".//span[@id='{sender_txn_id}']/text()")
            if not sender_txn_elements:
                # Fallback to tree-wide search but with exact ID match
                sender_txn_elements = tree.xpath(f"//span[@id='{sender_txn_id}']/text()")
            transaction["sender_transaction_id"] = clean_text(sender_txn_elements[0]) if sender_txn_elements else ""

            # 3. Sender bank name - use row-specific XPath
            sender_bank_id = f"ContentPlaceHolder1_gedotherbank_lblbankname_{row_idx}"
            sender_bank_elements = row.xpath(f".//span[@id='{sender_bank_id}']/text()")
            if not sender_bank_elements:
                # Fallback to tree-wide search but with exact ID match
                sender_bank_elements = tree.xpath(f"//span[@id='{sender_bank_id}']/text()")
            transaction["sender_bank"] = clean_text(sender_bank_elements[0]) if sender_bank_elements else ""

            # 4. Layer of the transaction - simple direct extraction
            # Just extract the layer value from the span with ID ContentPlaceHolder1_gedotherbank_lblplayers_X

            # Get all spans with IDs containing lblplayers
            all_layer_spans = tree.xpath("//span[contains(@id, 'ContentPlaceHolder1_gedotherbank_lblplayers')]")

            # Extract the layer value from the text content
            transaction["layer"] = ""  # Default to empty string

            # Loop through all spans and find the one for this row
            for span in all_layer_spans:
                # Get the ID of the span
                span_id = span.get('id', '')

                # Extract the index from the ID
                id_match = re.search(r'lblplayers_(\d+)', span_id)
                if id_match and id_match.group(1) == str(row_idx):
                    # This is the span for the current row
                    layer_text = clean_text(span.text_content())
                    # Extract the number from "Layer : X"
                    layer_match = re.search(r'\d+', layer_text)
                    if layer_match:
                        transaction["layer"] = layer_match.group(0)
                        logger.info(f"Found layer {transaction['layer']} for row {row_idx}")
                        break

            # If we didn't find a layer for this row, log a warning
            if transaction["layer"] is None:
                logger.warning(f"No layer found for row {row_idx}")

            # 5. Transaction type - use row-specific XPath
            type_id = f"ContentPlaceHolder1_gedotherbank_lblottype_{row_idx}"
            type_elements = row.xpath(f".//span[@id='{type_id}']/text()")
            if not type_elements:
                # Fallback to tree-wide search but with exact ID match
                type_elements = tree.xpath(f"//span[@id='{type_id}']/text()")
            transaction["txn_type"] = clean_text(type_elements[0]) if type_elements else ""
            # Keep type for backward compatibility
            transaction["type"] = transaction["txn_type"]

            # 6. Transaction date - extract from the div with "Txn Date:" in the 3rd cell
            # First, find the cell that contains the transaction type
            type_id = f"ContentPlaceHolder1_gedotherbank_lblottype_{row_idx}"
            type_element = row.xpath(f".//span[@id='{type_id}']")

            if type_element:
                # Get the parent cell (td) of the type element
                cell = type_element[0].xpath("./ancestor::td[1]")
                if cell:
                    # Look for a div with "Txn Date:" text
                    txn_date_div = cell[0].xpath(".//div[contains(., 'Txn Date:')]")
                    if txn_date_div:
                        # Extract the text and clean it
                        date_text = clean_text(txn_date_div[0].text_content())
                        # Remove the "Txn Date:" prefix
                        date_text = re.sub(r'^Txn\s*Date:\s*', '', date_text)
                        # Extract date using regex (including time if available)
                        date_match = re.search(r'\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}(\s+\d{1,2}:\d{1,2}:\d{1,2}\s*(?:AM|PM)?)?', date_text)
                        if date_match:
                            transaction["date"] = date_match.group(0)
                        else:
                            transaction["date"] = date_text.strip()
                    else:
                        # If no "Txn Date:" div, look for a div with "Date:" text
                        date_div = cell[0].xpath(".//div[contains(., 'Date:')]")
                        if date_div:
                            date_text = clean_text(date_div[0].text_content())
                            date_text = re.sub(r'^Date:\s*', '', date_text)
                            date_match = re.search(r'\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}(\s+\d{1,2}:\d{1,2}:\d{1,2}\s*(?:AM|PM)?)?', date_text)
                            if date_match:
                                transaction["date"] = date_match.group(0)
                            else:
                                transaction["date"] = date_text.strip()
                        else:
                            # If no specific date div, extract any date from the cell text
                            cell_text = cell[0].text_content()
                            date_match = re.search(r'\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}(\s+\d{1,2}:\d{1,2}:\d{1,2}\s*(?:AM|PM)?)?', cell_text)
                            if date_match:
                                transaction["date"] = date_match.group(0)
                            else:
                                transaction["date"] = ""
                else:
                    transaction["date"] = ""
            else:
                # If type element not found, try to find the date in the 3rd cell
                date_cell = row.xpath('.//td[3]')
                if date_cell:
                    # Look for a div with "Txn Date:" text
                    txn_date_div = date_cell[0].xpath(".//div[contains(., 'Txn Date:')]")
                    if txn_date_div:
                        date_text = clean_text(txn_date_div[0].text_content())
                        date_text = re.sub(r'^Txn\s*Date:\s*', '', date_text)
                        date_match = re.search(r'\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}(\s+\d{1,2}:\d{1,2}:\d{1,2}\s*(?:AM|PM)?)?', date_text)
                        if date_match:
                            transaction["date"] = date_match.group(0)
                        else:
                            transaction["date"] = date_text.strip()
                    else:
                        # If no specific date div, extract any date from the cell text
                        cell_text = date_cell[0].text_content()
                        date_match = re.search(r'\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}(\s+\d{1,2}:\d{1,2}:\d{1,2}\s*(?:AM|PM)?)?', cell_text)
                        if date_match:
                            transaction["date"] = date_match.group(0)
                        else:
                            transaction["date"] = ""
                else:
                    transaction["date"] = ""

            # 7. Receiver bank name - extract from the span in the 4th cell
            # First, try to find the 4th cell
            bank_cell = row.xpath('.//td[4]')
            if bank_cell:
                # Look for the first span in the cell (which should contain the bank name)
                bank_spans = bank_cell[0].xpath('.//span[1]/text()')
                if bank_spans:
                    transaction["receiver_bank"] = clean_text(bank_spans[0])
                else:
                    # If no span found, try to extract from the cell text
                    cell_text = clean_text(bank_cell[0].text_content())
                    # If the cell text is not empty, use it as the bank name
                    if cell_text:
                        transaction["receiver_bank"] = cell_text
                    else:
                        # If still no bank name, try specific element ID
                        bank_id = f"ContentPlaceHolder1_gedotherbank_lblbanknamea_{row_idx}"
                        bank_elements = row.xpath(f".//span[@id='{bank_id}']/text()")
                        if bank_elements:
                            transaction["receiver_bank"] = clean_text(bank_elements[0])
                        else:
                            # If still no bank name, use a default
                            transaction["receiver_bank"] = ""

            # 8. Receiver bank account - use row-specific XPath with enhanced fallback
            receiver_account_id = f"ContentPlaceHolder1_gedotherbank_lblacounta_{row_idx}"
            receiver_account_elements = row.xpath(f".//span[@id='{receiver_account_id}']/text()")

            if not receiver_account_elements:
                # Fallback to tree-wide search but with exact ID match
                receiver_account_elements = tree.xpath(f"//span[@id='{receiver_account_id}']/text()")

            # If still no receiver account found, try the new fallback mechanism
            if not receiver_account_elements:
                logger.debug(f"Row {row_idx}: No receiver account found with ID {receiver_account_id}, trying fallback")

                # Find the cell that should contain the receiver account (usually the 6th cell)
                account_cell = row.xpath('.//td[6]')
                if account_cell and len(account_cell) > 0:
                    # Use the helper function to extract merchant info
                    merchant_info = extract_merchant_info(account_cell[0])

                    if merchant_info:
                        # Use the merchant info as the receiver account
                        transaction["receiver_account"] = merchant_info
                        logger.debug(f"Row {row_idx}: Using merchant info as receiver account: {merchant_info}")
                    else:
                        # No valid merchant info found, keep field blank
                        transaction["receiver_account"] = ""
                        logger.debug(f"Row {row_idx}: No valid merchant info found, keeping field blank")
                else:
                    transaction["receiver_account"] = ""
            else:
                # Use the found receiver account
                transaction["receiver_account"] = clean_account_number(clean_text(receiver_account_elements[0]) if receiver_account_elements else "")

            # Check for IFSC code in the same cell
            ifsc_elements = row.xpath(f".//td[6]//div[contains(text(), 'IFSC')]/text()")
            if ifsc_elements:
                ifsc_text = clean_text(ifsc_elements[0])
                ifsc_match = re.search(r'IFSC\s*:?\s*([A-Z0-9]+)', ifsc_text, re.IGNORECASE)
                if ifsc_match:
                    transaction["receiver_ifsc"] = ifsc_match.group(1)

            # 9. Receiver transaction ID - use row-specific XPath with fallback
            receiver_txn_id = f"ContentPlaceHolder1_gedotherbank_lbltransid_{row_idx}"
            receiver_txn_elements = row.xpath(f".//span[@id='{receiver_txn_id}']/text()")
            if not receiver_txn_elements:
                # Fallback to tree-wide search but with exact ID match
                receiver_txn_elements = tree.xpath(f"//span[@id='{receiver_txn_id}']/text()")
            transaction["receiver_transaction_id"] = clean_text(receiver_txn_elements[0]) if receiver_txn_elements else ""

            # 10. Transaction amount - use row-specific XPath with fallback
            amount_id = f"ContentPlaceHolder1_gedotherbank_lbloamnt_{row_idx}"
            amount_elements = row.xpath(f".//span[@id='{amount_id}']/text()")
            if not amount_elements:
                # Fallback to tree-wide search but with exact ID match
                amount_elements = tree.xpath(f"//span[@id='{amount_id}']/text()")
            amount_text = clean_text(amount_elements[0]) if amount_elements else ""

            # Clean amount (remove currency symbols, commas, etc.)
            if amount_text:
                # Remove currency symbols and commas
                amount_text = amount_text.replace('₹', '').replace('Rs.', '').replace('Rs', '').replace(',', '').strip()
                # Extract numeric part
                amount_match = re.search(r'\d+(\.\d+)?', amount_text)
                if amount_match:
                    transaction["amount"] = amount_match.group(0)
                else:
                    transaction["amount"] = amount_text
            else:
                transaction["amount"] = ""

            # Extract additional information for the reference column
            # This should include columns 7, 8, and 9 (cheque numbers, remarks, etc.)
            # We'll combine them without creating new rows and without column names

            # Extract column 7 (usually cheque number or reference number)
            col7_cell = row.xpath('.//td[7]')
            col7_text = clean_text(col7_cell[0].text_content()) if col7_cell else ""

            # Extract column 8 (usually remarks or additional information)
            col8_cell = row.xpath('.//td[8]')
            col8_text = clean_text(col8_cell[0].text_content()) if col8_cell else ""

            # Extract column 9 (usually additional remarks)
            col9_cell = row.xpath('.//td[9]')
            col9_text = clean_text(col9_cell[0].text_content()) if col9_cell else ""

            # Also try to extract using specific element IDs as a backup
            # Try to extract cheque number
            cheque_id = f"ContentPlaceHolder1_gedotherbank_lblchequeno_{row_idx}"
            cheque_elements = row.xpath(f".//span[@id='{cheque_id}']/text()")
            cheque_number = clean_text(cheque_elements[0]) if cheque_elements else ""

            # Try to extract remarks or additional information
            remarks_id = f"ContentPlaceHolder1_gedotherbank_lblremarks_{row_idx}"
            remarks_elements = row.xpath(f".//span[@id='{remarks_id}']/text()")
            remarks = clean_text(remarks_elements[0]) if remarks_elements else ""

            # Try to extract reference number
            ref_id = f"ContentPlaceHolder1_gedotherbank_lblrefno_{row_idx}"
            ref_elements = row.xpath(f".//span[@id='{ref_id}']/text()")
            ref_number = clean_text(ref_elements[0]) if ref_elements else ""

            # Combine all reference information into a single string
            reference_text = ""

            # Use cheque number if available, otherwise use column 7 text
            if cheque_number:
                reference_text += cheque_number
            elif col7_text:
                reference_text += col7_text

            # Add remarks or column 8 text if available
            if remarks and remarks not in reference_text:
                reference_text += " " + remarks if reference_text else remarks
            elif col8_text and col8_text not in reference_text:
                reference_text += " " + col8_text if reference_text else col8_text

            # Add column 9 text if available and not already included
            if col9_text and col9_text not in reference_text:
                reference_text += " " + col9_text if reference_text else col9_text

            # Add reference number if available and not already included
            if ref_number and ref_number not in reference_text:
                reference_text += " " + ref_number if reference_text else ref_number

            # Set the reference column with the combined information
            transaction["receiver_info"] = reference_text.strip()

            # Skip rows with missing critical data
            if not transaction["sender_account"] or not transaction["amount"]:
                logger.warning(f"Skipping row {row_idx} due to missing critical data")
                continue

            # Check if this transaction is a duplicate of an existing one
            is_duplicate = False
            for existing_txn in transactions:
                # Check if sender account, receiver account, amount, and transaction ID match
                if (existing_txn.get("sender_account") == transaction.get("sender_account") and
                    existing_txn.get("receiver_account") == transaction.get("receiver_account") and
                    existing_txn.get("amount") == transaction.get("amount") and
                    existing_txn.get("receiver_transaction_id") == transaction.get("receiver_transaction_id")):
                    is_duplicate = True
                    logger.warning(f"Skipping duplicate transaction for account {transaction.get('sender_account')}")
                    break

            # Only add if not a duplicate
            if not is_duplicate:
                transactions.append(transaction)

        except Exception as e:
            logger.error(f"Error processing row {row_idx} in Banking/UPI extraction: {str(e)}")
            continue

    logger.info(f"Extracted {len(transactions)} Banking/UPI transactions")
    return transactions

def extract_with_custom_logic(tree, extraction_type="default", max_layer=None):
    """
    Main function to extract data using different extraction logic based on the type.
    This is a placeholder for different extraction methods that will be implemented.

    Args:
        tree: HTML tree parsed with lxml
        extraction_type: Type of extraction logic to use
        max_layer: If provided, only return transactions with layer <= max_layer

    Returns:
        List of extracted transactions
    """
    logger.info(f"Using extraction logic: {extraction_type}")

    # Extract tables
    main_table_rows = extract_transaction_table(tree)
    victim_table_rows = extract_victim_transaction_table(tree)

    # Initialize transactions list
    transactions = []

    # Check if main table is empty but victim table has data
    main_table_empty = not main_table_rows
    victim_table_has_data = bool(victim_table_rows)

    if main_table_empty and victim_table_has_data:
        logger.info("Main transaction table not found, using victim's transaction table as primary source")
        # Use victim table as the main table
        main_table_rows = victim_table_rows
        # Clear victim table to avoid duplication
        victim_table_rows = []

    # Select extraction logic based on type
    if extraction_type == "banking_upi" or extraction_type == "online_banking":
        # Always use the ID-based extraction for banking/UPI
        logger.info("Using ID-based extraction for banking/UPI transactions")
        # Force extraction_type to banking_upi for consistency
        extraction_type = "banking_upi"
        transactions = extract_banking_upi_transactions(tree)

    elif extraction_type == "card":
        # For card transactions, we need to pass the tree, not the rows
        logger.info("Using card extraction logic")
        transactions = extract_card_transactions(tree)

        # Ensure all transactions have the correct fraud type
        for txn in transactions:
            txn['fraud_type'] = 'card'
            logger.debug(f"Set fraud_type to 'card' for transaction with layer {txn.get('layer')}")

        # Note: We're intentionally not processing the victim table as per requirements
        # The victim table is only for reference and not included in the extracted data

    else:  # Default extraction logic
        # Default to ID-based extraction for banking/UPI
        logger.info("No specific extraction type provided, defaulting to ID-based extraction")
        transactions = extract_banking_upi_transactions(tree)

    # Organize transactions by their relationships
    logger.info(f"Organizing {len(transactions)} transactions by their relationships")
    organized_transactions = organize_transactions_by_relationships(transactions)
    logger.info(f"Organized transactions: {len(organized_transactions)} total")

    # Filter by max_layer if provided
    if max_layer is not None:
        filtered_transactions = []
        for txn in organized_transactions:
            layer_value = txn.get("layer", 0)
            # Convert layer value to int if it's a string
            if layer_value is not None:
                try:
                    layer_value = int(layer_value)
                    if layer_value <= max_layer:
                        filtered_transactions.append(txn)
                except (ValueError, TypeError):
                    # If conversion fails, log warning and skip this transaction
                    logger.warning(f"Invalid layer value: {layer_value}, skipping transaction")

        logger.info(f"Filtered transactions by max_layer={max_layer}: {len(filtered_transactions)} remain")
        return filtered_transactions

    return organized_transactions

def extract_transaction_table(tree, table_id="ContentPlaceHolder1_gedotherbank"):
    """
    Extract transaction data from the main transaction table with the specified ID.

    Args:
        tree: HTML tree parsed with lxml
        table_id: ID of the table to extract (default: ContentPlaceHolder1_gedotherbank)

    Returns:
        List of rows from the table, each row is a list of td elements
    """
    logger.info(f"Attempting to extract transaction table with ID: {table_id}")

    # Try to find the table by ID
    table = tree.xpath(f"//table[@id='{table_id}']")

    logger.info(f"Table with ID '{table_id}' found: {bool(table)}")

    if not table:
        logger.warning(f"Table with ID '{table_id}' not found")
        # Fallback to any table that might contain transaction data
        table = tree.xpath('//table')
        if not table:
            logger.error("No tables found in the document")
            return []
        else:
            logger.info(f"Using fallback: first table of {len(table)} tables found")

    # Get all rows from the table, skip the header row
    rows = table[0].xpath('.//tr')[1:] if table else []

    # Log row count
    logger.info(f"Found {len(rows)} rows in the table")

    # If no rows found with standard XPath, try alternative methods
    if not rows and table:
        logger.info("Trying alternative XPath queries to find rows")

        # Try direct child rows
        direct_rows = table[0].xpath('./tr[position() > 1]')

        # Try with tbody
        tbody_rows = table[0].xpath('./tbody/tr')

        # Try with different case for tr
        case_rows = table[0].xpath('.//*[local-name()="tr" or local-name()="TR"][position() > 1]')

        # If any of these methods found rows, use them
        if direct_rows:
            logger.info(f"Using direct child rows: {len(direct_rows)} found")
            rows = direct_rows
        elif tbody_rows:
            logger.info(f"Using tbody rows: {len(tbody_rows)} found")
            rows = tbody_rows
        elif case_rows:
            logger.info(f"Using case-insensitive rows: {len(case_rows)} found")
            rows = case_rows

    if not rows:
        logger.warning(f"No rows found in table with ID '{table_id}' after all attempts")

    return rows

def extract_online_banking_transactions(rows):
    """
    Extract Online/UPI/Net Banking fraud transactions from the given table rows.

    Args:
        rows: List of table rows (tr elements)

    Returns:
        List of transaction dictionaries
    """
    logger.info("Extracting Online/UPI/Net Banking fraud transactions")
    transactions = []

    # Keywords specific to online banking transactions
    online_keywords = ["upi", "imps", "neft", "rtgs", "internet banking", "net banking", "online"]

    for row_idx, row in enumerate(rows):
        # Check if row has enough columns
        cols = row.xpath('.//td')
        if len(cols) < 10:
            continue

        # Extract remarks to check if we should skip this row
        remarks_col = 9 if len(cols) > 9 else -1
        remarks = clean_text(cols[remarks_col].text_content()) if remarks_col >= 0 else ""

        if should_skip_row(remarks):
            continue

        # Extract transaction data
        account_no = clean_text(cols[1].text_content()) if len(cols) > 1 else ""
        account_holder = clean_text(cols[2].text_content()) if len(cols) > 2 else ""
        txn_date = clean_text(cols[3].text_content()) if len(cols) > 3 else ""
        txn_type = clean_text(cols[4].text_content()) if len(cols) > 4 else ""
        amount = clean_text(cols[5].text_content()) if len(cols) > 5 else ""

        # Skip if missing critical data
        if not account_no or not txn_type or not amount:
            continue

        # Extract additional data
        bank_name = clean_text(cols[0].text_content()) if len(cols) > 0 else ""
        transaction_id = clean_text(cols[7].text_content()) if len(cols) > 7 else ""
        transaction_remarks = clean_text(cols[8].text_content()) if len(cols) > 8 else ""

        # Normalize transaction type
        txn_type_lower = txn_type.lower()

        # Check if this is an online banking transaction
        is_online_banking = False
        for keyword in online_keywords:
            if keyword in txn_type_lower or keyword in transaction_remarks.lower() or keyword in remarks.lower():
                is_online_banking = True
                break

        # Skip if not an online banking transaction
        if not is_online_banking and any(keyword in txn_type_lower for keyword in ["card", "atm", "pos"]):
            continue

        # Determine transaction direction and layer
        is_credit = False
        layer = 0

        if "transfer to" in txn_type_lower or "paid to" in txn_type_lower:
            is_credit = False  # Money going out
            layer = 1  # First layer transaction
        elif "transfer from" in txn_type_lower or "received from" in txn_type_lower:
            is_credit = True  # Money coming in
            layer = 0  # Root layer transaction
        elif "credit" in txn_type_lower or "deposit" in txn_type_lower:
            is_credit = True
            layer = 0
        elif "debit" in txn_type_lower or "withdrawal" in txn_type_lower:
            is_credit = False
            layer = 1

        # Create transaction object
        transaction = {
            "bank_name": bank_name,
            "account_no": account_no,
            "account_holder": account_holder,
            "date": txn_date,
            "txn_type": txn_type,
            "type": txn_type,  # Keep for backward compatibility
            "amount": amount,
            "cheque_no": "",  # Not relevant for online banking
            "transaction_id": transaction_id,
            "receiver_info": transaction_remarks,
            "remarks": transaction_remarks,  # Keep for backward compatibility
            "additional_remarks": remarks,
            "is_credit": is_credit,
            "layer": layer,
            "row_idx": row_idx,
            "fraud_type": "online_banking"
        }

        # Extract UPI ID if present
        upi_match = re.search(r'[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+', transaction_remarks)
        if upi_match:
            transaction["upi_id"] = upi_match.group(0)

        # Extract IMPS/NEFT reference if present
        ref_match = re.search(r'(?:IMPS|NEFT|RTGS)\s*[A-Z0-9]+', transaction_remarks)
        if ref_match:
            transaction["reference_no"] = ref_match.group(0)

        transactions.append(transaction)

    return transactions

def extract_card_transactions(tree):
    """
    Extract Credit/Debit Card transactions using specific element IDs.
    This function extracts data directly from the HTML elements using their IDs
    similar to the banking_upi extraction but with card-specific fields.
    It only processes the main transaction table, not the victim table.

    Args:
        tree: HTML tree parsed with lxml

    Returns:
        List of transaction dictionaries with card-specific fields
    """
    logger.info("Extracting Credit/Debit Card transactions using element IDs")
    transactions = []

    # Find the main transaction table
    main_table = tree.xpath("//table[@id='ContentPlaceHolder1_gedotherbank']")
    if not main_table:
        logger.warning("Main transaction table not found for Card extraction")
        return transactions  # Return empty list if main table not found

    # Get all rows from the table (skip header)
    rows = main_table[0].xpath('.//tr')[1:] if main_table else []
    if not rows:
        logger.warning("No rows found in main transaction table for Card extraction")
        return transactions  # Return empty list if no rows found

    # Process main table rows only
    transactions.extend(_process_card_transaction_rows(tree, rows))

    # Note: We're intentionally not processing the victim table as per requirements
    # The victim table (ContentPlaceHolder1_gv_ActiontakenStatus) is only for reference

    return transactions

def _process_card_transaction_rows(tree, rows):
    """
    Process rows from a card transaction table.

    Args:
        tree: HTML tree parsed with lxml
        rows: List of table rows to process

    Returns:
        List of transaction dictionaries
    """
    transactions = []

    # Process each row
    for row_idx, row in enumerate(rows):
        try:
            # Initialize transaction object with only the required fields
            transaction = {
                "fraud_type": "card",  # Explicitly set fraud type to card for card extraction
                "layer": "",
                "type": "",
                "date": "",
                "sender_account": "",
                "sender_transaction_id": "",
                "sender_bank": "",
                "receiver_bank": "",
                "receiver_account": "",
                "receiver_transaction_id": "",
                "amount": "",
                "reference": ""
            }

            # Extract data using the specific element IDs
            # 1. Sender bank account - use row-specific XPath
            sender_account_id = f"ContentPlaceHolder1_gedotherbank_lblppprootaccntidmany_{row_idx}"
            sender_account_elements = row.xpath(f".//span[@id='{sender_account_id}']/text()")
            if not sender_account_elements:
                # Fallback to tree-wide search but with exact ID match
                sender_account_elements = tree.xpath(f"//span[@id='{sender_account_id}']/text()")

            # Set sender account and clean it
            sender_account = clean_account_number(clean_text(sender_account_elements[0]) if sender_account_elements else "")
            transaction["sender_account"] = sender_account

            # Log the sender account for debugging
            logger.debug(f"Row {row_idx}: Sender account extracted: '{sender_account}'")

            # 2. Sender transaction ID
            sender_txn_id = f"ContentPlaceHolder1_gedotherbank_lblpproottransidmany_{row_idx}"
            sender_txn_elements = tree.xpath(f"//span[@id='{sender_txn_id}']/text()")
            transaction["sender_transaction_id"] = clean_text(sender_txn_elements[0]) if sender_txn_elements else ""

            # 3. Sender bank name
            sender_bank_id = f"ContentPlaceHolder1_gedotherbank_lblbankname_{row_idx}"
            sender_bank_elements = tree.xpath(f"//span[@id='{sender_bank_id}']/text()")
            transaction["sender_bank"] = clean_text(sender_bank_elements[0]) if sender_bank_elements else ""

            # 4. Layer
            layer_id = f"ContentPlaceHolder1_gedotherbank_lblplayers_{row_idx}"
            layer_elements = tree.xpath(f"//span[@id='{layer_id}']/text()")
            if layer_elements:
                layer_text = clean_text(layer_elements[0])
                layer_match = re.search(r'\d+', layer_text)
                if layer_match:
                    transaction["layer"] = layer_match.group(0)

            # 5. Transaction type
            type_id = f"ContentPlaceHolder1_gedotherbank_lblottype_{row_idx}"
            type_elements = tree.xpath(f"//span[@id='{type_id}']/text()")
            transaction["txn_type"] = clean_text(type_elements[0]) if type_elements else ""
            # Keep type for backward compatibility
            transaction["type"] = transaction["txn_type"]

            # 6. Transaction date
            # Try to find date in various locations
            date_found = False

            # Try to find date in a div with "Txn Date:" text
            date_divs = tree.xpath(f"//div[contains(., 'Txn Date:')]")
            for div in date_divs:
                date_text = clean_text(div.text_content())
                date_text = re.sub(r'^Txn\s*Date:\s*', '', date_text)
                date_match = re.search(r'\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}(\s+\d{1,2}:\d{1,2}:\d{1,2}\s*(?:AM|PM)?)?', date_text)
                if date_match:
                    transaction["date"] = date_match.group(0)
                    date_found = True
                    break

            # If date not found, try to find it in a div with "Date:" text
            if not date_found:
                date_divs = tree.xpath(f"//div[contains(., 'Date:')]")
                for div in date_divs:
                    date_text = clean_text(div.text_content())
                    date_text = re.sub(r'^Date:\s*', '', date_text)
                    date_match = re.search(r'\d{1,2}[/.-]\d{1,2}[/.-]\d{2,4}(\s+\d{1,2}:\d{1,2}:\d{1,2}\s*(?:AM|PM)?)?', date_text)
                    if date_match:
                        transaction["date"] = date_match.group(0)
                        date_found = True
                        break

            # 7. Receiver bank name - use more robust approach from banking_upi extraction
            # First, try to find the 4th cell
            bank_cell = row.xpath('.//td[4]')
            if bank_cell:
                # Extract all content from the cell
                bank_name = ""

                # Look for the first span in the cell (which should contain the bank name)
                bank_spans = bank_cell[0].xpath('.//span[1]/text()')
                if bank_spans:
                    bank_name = clean_text(bank_spans[0])

                # Check if bank name contains "Others"
                if "Others" in bank_name:
                    # Try to find additional bank name in all divs
                    additional_bank_divs = bank_cell[0].xpath('.//div')
                    additional_bank_name = ""

                    for div in additional_bank_divs:
                        div_text = clean_text(div.text_content())

                        # Skip empty divs or divs with just these phrases
                        if not div_text or div_text == "Merchant :" or div_text == "Wallet /PG/PA :":
                            continue

                        # Remove specific phrases if they exist
                        div_text = div_text.replace("Merchant :", "").replace("Wallet /PG/PA :", "").strip()

                        if div_text:
                            additional_bank_name = div_text
                            break

                    # If we found additional bank name, use it instead of "Others"
                    if additional_bank_name:
                        bank_name = additional_bank_name

                transaction["receiver_bank"] = bank_name

                # If bank name is still empty, try other methods
                if not transaction["receiver_bank"]:
                    # Try to extract from the cell text, excluding the empty fields
                    cell_text = clean_text(bank_cell[0].text_content())
                    cell_text = cell_text.replace("Others", "").replace("Merchant :", "").replace("Wallet /PG/PA :", "").strip()

                    # If the cell text is not empty, use it as the bank name
                    if cell_text:
                        transaction["receiver_bank"] = cell_text
                    else:
                        # If still no bank name, try specific element ID
                        bank_id = f"ContentPlaceHolder1_gedotherbank_lblbanknamea_{row_idx}"
                        bank_elements = row.xpath(f".//span[@id='{bank_id}']/text()")
                        if bank_elements:
                            transaction["receiver_bank"] = clean_text(bank_elements[0])
                        else:
                            # Fallback to tree-wide search with exact ID match
                            bank_elements = tree.xpath(f"//span[@id='{bank_id}']/text()")
                            transaction["receiver_bank"] = clean_text(bank_elements[0]) if bank_elements else ""
            else:
                # If no 4th cell, try specific element ID
                bank_id = f"ContentPlaceHolder1_gedotherbank_lblbanknamea_{row_idx}"
                bank_elements = tree.xpath(f"//span[@id='{bank_id}']/text()")
                transaction["receiver_bank"] = clean_text(bank_elements[0]) if bank_elements else ""

            # 8. Receiver bank account with enhanced fallback
            receiver_account_id = f"ContentPlaceHolder1_gedotherbank_lblacounta_{row_idx}"
            receiver_account_elements = tree.xpath(f"//span[@id='{receiver_account_id}']/text()")

            # If no receiver account found, try the fallback mechanism
            if not receiver_account_elements:
                logger.debug(f"Row {row_idx}: No receiver account found with ID {receiver_account_id}, trying fallback")

                # Find the cell that should contain the receiver account (usually the 6th cell)
                account_cell = row.xpath('.//td[6]')
                if account_cell and len(account_cell) > 0:
                    # Use the helper function to extract merchant info
                    merchant_info = extract_merchant_info(account_cell[0])

                    if merchant_info:
                        # Use the merchant info as the receiver account
                        receiver_account = merchant_info
                        logger.debug(f"Row {row_idx}: Using merchant info as receiver account: {merchant_info}")
                    else:
                        # No valid merchant info found, keep field blank
                        receiver_account = ""
                        logger.debug(f"Row {row_idx}: No valid merchant info found, keeping field blank")
                else:
                    receiver_account = ""
            else:
                # Use the found receiver account
                receiver_account = clean_account_number(clean_text(receiver_account_elements[0]) if receiver_account_elements else "")

            transaction["receiver_account"] = receiver_account

            # Log the receiver account for debugging
            logger.debug(f"Row {row_idx}: Receiver account extracted: '{receiver_account}'")

            # 9. Receiver transaction ID
            receiver_txn_id = f"ContentPlaceHolder1_gedotherbank_lbltransid_{row_idx}"
            receiver_txn_elements = tree.xpath(f"//span[@id='{receiver_txn_id}']/text()")
            receiver_transaction_id = clean_text(receiver_txn_elements[0]) if receiver_txn_elements else ""
            transaction["receiver_transaction_id"] = receiver_transaction_id

            # Apply fallback logic directly in extraction for Layer 1 transactions
            # If sender account is empty but sender transaction ID matches receiver account
            # and receiver account is a 16-digit number, use receiver account as sender account
            sender_account = transaction.get("sender_account", "")
            sender_txn_id = transaction.get("sender_transaction_id", "")
            receiver_account = transaction.get("receiver_account", "")
            receiver_txn_id = transaction.get("receiver_transaction_id", "")

            # Get layer value and convert to string for comparison
            layer_value = transaction.get("layer", "")
            is_layer_1 = str(layer_value) == "1"

            # Log the layer value for debugging
            logger.debug(f"Row {row_idx}: Layer value: {layer_value}, is_layer_1: {is_layer_1}")

            # Only apply fallback logic for Layer 1 transactions with fraud_type "card"
            if is_layer_1 and transaction.get("fraud_type") == "card":
                fallback_applied = False
                warning_message = None

                # Get the transaction amount for matching with victim's table
                amount = transaction.get("amount", "")

                # Find the victim's transaction table once for all fallbacks
                victim_table = tree.xpath("//table[@id='ContentPlaceHolder1_gv_ActiontakenStatus']")
                victim_rows = victim_table[0].xpath('.//tr')[1:] if victim_table else []

                logger.info(f"Row {row_idx}: Starting fallback logic for Layer 1 card transaction")
                logger.info(f"Row {row_idx}: Sender account: '{sender_account}', Sender txn ID: '{sender_txn_id}', Receiver account: '{receiver_account}'")

                # FALLBACK 1: If only sender transaction ID is present, match with victim's table transaction ID
                if (not sender_account or sender_account.strip() == "") and sender_txn_id:
                    logger.info(f"Row {row_idx}: Trying Fallback 1 - Matching sender transaction ID with victim's table")

                    if victim_table:
                        # Check each row in the victim table
                        for v_idx, v_row in enumerate(victim_rows):
                            # Extract transaction ID from victim's table
                            v_txn_id_element = v_row.xpath(f".//span[@id='ContentPlaceHolder1_gv_ActiontakenStatus_lbltransid_{v_idx}']/text()")
                            if not v_txn_id_element:
                                # Fallback to tree-wide search but with exact ID match
                                v_txn_id_element = tree.xpath(f"//span[@id='ContentPlaceHolder1_gv_ActiontakenStatus_lbltransid_{v_idx}']/text()")
                            v_txn_id = clean_text(v_txn_id_element[0]) if v_txn_id_element else ""

                            logger.debug(f"Row {row_idx}: Comparing victim txn ID: '{v_txn_id}' with sender txn ID: '{sender_txn_id}'")

                            # If transaction IDs match and receiver account looks like a card number (13-19 digits), use it as sender account
                            if v_txn_id and sender_txn_id and v_txn_id == sender_txn_id:
                                # More flexible check for card numbers (13-19 digits is standard range for card numbers)
                                if receiver_account and receiver_account.isdigit() and 13 <= len(receiver_account) <= 19:
                                    logger.info(f"Row {row_idx}: FALLBACK 1 APPLIED - Found matching transaction ID in victim's table. Victim txn ID: '{v_txn_id}', Sender txn ID: '{sender_txn_id}'")
                                    transaction["sender_account"] = receiver_account
                                    fallback_applied = True
                                    break
                                # Even if receiver account doesn't look like a card number, still use it if it's the only option
                                elif receiver_account and receiver_account.strip():
                                    logger.info(f"Row {row_idx}: FALLBACK 1 APPLIED (with non-standard card format) - Found matching transaction ID. Using receiver account: '{receiver_account}'")
                                    transaction["sender_account"] = receiver_account
                                    fallback_applied = True
                                    break
                                else:
                                    logger.info(f"Row {row_idx}: Fallback 1 match found but receiver account '{receiver_account}' is empty or invalid")

                    if not fallback_applied:
                        logger.info(f"Row {row_idx}: Fallback 1 not applied - No matching transaction ID found in victim's table")
                else:
                    logger.info(f"Row {row_idx}: Skipping Fallback 1 - {'Sender account already present' if sender_account else 'No sender transaction ID'}")

                # FALLBACK 2: If both sender account and sender transaction ID are missing, match amount and card details
                if not fallback_applied and (not sender_account or sender_account.strip() == "") and (not sender_txn_id or sender_txn_id.strip() == ""):
                    logger.info(f"Row {row_idx}: Trying Fallback 2 - Matching amount and card details with victim's table")

                    if victim_table and amount:
                        # Check each row in the victim table
                        for v_idx, v_row in enumerate(victim_rows):
                            # Extract amount from victim's table
                            v_amount_element = v_row.xpath(f".//span[@id='ContentPlaceHolder1_gv_ActiontakenStatus_lblamnt_{v_idx}']/text()")
                            if not v_amount_element:
                                # Fallback to tree-wide search but with exact ID match
                                v_amount_element = tree.xpath(f"//span[@id='ContentPlaceHolder1_gv_ActiontakenStatus_lblamnt_{v_idx}']/text()")
                            v_amount = clean_text(v_amount_element[0]) if v_amount_element else ""

                            # If amounts don't match, skip this row
                            if not v_amount or v_amount != amount:
                                logger.debug(f"Row {row_idx}: Skipping victim row {v_idx} - Amount mismatch: '{v_amount}' vs '{amount}'")
                                continue

                            logger.info(f"Row {row_idx}: Found matching amount in victim row {v_idx}: '{v_amount}'")

                            # Find the card details column (typically the 4th column)
                            card_cell = v_row.xpath('.//td[4]')
                            if card_cell:
                                # Get the full cell content for debugging
                                cell_content = clean_text(card_cell[0].text_content())
                                logger.debug(f"Row {row_idx}: Card cell content for victim row {v_idx}: {cell_content}")

                                # Extract first six digits
                                first_six_match = re.search(r'First Six Digit:\s*(\d+)', cell_content)
                                first_six = first_six_match.group(1) if first_six_match else ""

                                # Extract last four digits
                                last_four_match = re.search(r'Last Four Digit:\s*(\d+)', cell_content)
                                last_four = last_four_match.group(1) if last_four_match else ""

                                logger.debug(f"Row {row_idx}: Extracted First Six: '{first_six}', Last Four: '{last_four}' from victim row {v_idx}")

                                # More flexible check for receiver account
                                is_valid_card = False
                                if receiver_account and receiver_account.isdigit() and 13 <= len(receiver_account) <= 19:
                                    is_valid_card = True
                                    logger.info(f"Row {row_idx}: Receiver account '{receiver_account}' is a valid card number format")
                                elif not receiver_account or not receiver_account.strip():
                                    logger.info(f"Row {row_idx}: Receiver account is empty or invalid: '{receiver_account}'")
                                    continue
                                else:
                                    logger.info(f"Row {row_idx}: Receiver account '{receiver_account}' is not in standard card format but will still check for matches")

                                # Check if first six digits match
                                first_six_match = False
                                if first_six and receiver_account and receiver_account.startswith(first_six):
                                    first_six_match = True
                                    logger.info(f"Row {row_idx}: First six digits match: '{first_six}' with receiver account '{receiver_account}'")

                                # Check if last four digits match
                                last_four_match = False
                                if last_four and receiver_account and receiver_account.endswith(last_four):
                                    last_four_match = True
                                    logger.info(f"Row {row_idx}: Last four digits match: '{last_four}' with receiver account '{receiver_account}'")

                                # Apply fallback if either first six or last four digits match
                                if first_six_match or last_four_match:
                                    match_type = "first six digits" if first_six_match else "last four digits"
                                    logger.info(f"Row {row_idx}: FALLBACK 2 APPLIED - {match_type} match with receiver account '{receiver_account}'")
                                    transaction["sender_account"] = receiver_account
                                    fallback_applied = True
                                    break
                                # If it's a valid card number but no match, still use it if it's the only option and we have matching amount
                                elif is_valid_card and v_amount and amount and v_amount == amount:
                                    logger.info(f"Row {row_idx}: FALLBACK 2 APPLIED (amount match only) - Using receiver account '{receiver_account}' based on matching amount")
                                    transaction["sender_account"] = receiver_account
                                    fallback_applied = True
                                    break
                                else:
                                    logger.info(f"Row {row_idx}: No card number match found for receiver account '{receiver_account}'")

                    if not fallback_applied:
                        logger.info(f"Row {row_idx}: Fallback 2 not applied - {'No matching amount found' if amount else 'No amount to match'}")
                else:
                    logger.info(f"Row {row_idx}: Skipping Fallback 2 - {'Sender account already present' if sender_account else 'Sender transaction ID present'}")

                # FALLBACK 3: If sender account is empty but sender transaction ID matches receiver account
                # and receiver account looks like a card number (13-19 digits), use receiver account as sender account
                if not fallback_applied and (not sender_account or sender_account.strip() == "") and sender_txn_id and receiver_account:
                    # Check if sender_txn_id exactly matches receiver_account
                    exact_match = sender_txn_id == receiver_account

                    # Check if receiver_account is a valid card number (13-19 digits)
                    is_card_number = receiver_account.isdigit() and 13 <= len(receiver_account) <= 19

                    # Check if sender_txn_id is contained within receiver_account or vice versa
                    contains_match = sender_txn_id in receiver_account or receiver_account in sender_txn_id

                    # Apply fallback if there's an exact match and it's a card number
                    if exact_match and is_card_number:
                        logger.info(f"Row {row_idx}: FALLBACK 3 APPLIED - Sender transaction ID '{sender_txn_id}' exactly matches receiver account '{receiver_account}'")
                        transaction["sender_account"] = receiver_account
                        transaction["sender_transaction_id"] = ""
                        fallback_applied = True
                    # Or if there's an exact match even if it's not a standard card number format
                    elif exact_match:
                        logger.info(f"Row {row_idx}: FALLBACK 3 APPLIED (non-standard format) - Sender transaction ID '{sender_txn_id}' matches receiver account '{receiver_account}'")
                        transaction["sender_account"] = receiver_account
                        transaction["sender_transaction_id"] = ""
                        fallback_applied = True
                    # Or if there's a partial match and receiver account is a valid card number
                    elif contains_match and is_card_number:
                        logger.info(f"Row {row_idx}: FALLBACK 3 APPLIED (partial match) - Sender transaction ID '{sender_txn_id}' partially matches receiver account '{receiver_account}'")
                        transaction["sender_account"] = receiver_account
                        transaction["sender_transaction_id"] = ""
                        fallback_applied = True
                    else:
                        logger.info(f"Row {row_idx}: Fallback 3 not applied - No match between sender txn ID '{sender_txn_id}' and receiver account '{receiver_account}'")

                # If all fallbacks failed, set a warning message
                if not fallback_applied and (not sender_account or sender_account.strip() == ""):
                    warning_message = "Card details not found. Please update the table to generate proper graph and notice."
                    logger.warning(f"Row {row_idx}: ALL FALLBACKS FAILED - No sender account found")
                    # We'll continue with whatever data we have
                    transaction["warning"] = warning_message

                # Log the final result of fallback logic
                if fallback_applied:
                    logger.info(f"Row {row_idx}: FALLBACK SUMMARY - Updated sender_account={transaction.get('sender_account', '')}")
                elif warning_message:
                    logger.info(f"Row {row_idx}: FALLBACK SUMMARY - Warning set: {warning_message}")
                else:
                    logger.info(f"Row {row_idx}: FALLBACK SUMMARY - No fallback needed, sender account already present: {sender_account}")

            # 10. Transaction amount
            amount_id = f"ContentPlaceHolder1_gedotherbank_lbloamnt_{row_idx}"
            amount_elements = tree.xpath(f"//span[@id='{amount_id}']/text()")
            amount_text = clean_text(amount_elements[0]) if amount_elements else ""

            # Clean amount (remove currency symbols, commas, etc.)
            if amount_text:
                # Remove currency symbols and commas
                amount_text = amount_text.replace('₹', '').replace('Rs.', '').replace('Rs', '').replace(',', '').strip()
                # Extract numeric part
                amount_match = re.search(r'\d+(\.\d+)?', amount_text)
                if amount_match:
                    transaction["amount"] = amount_match.group(0)
                else:
                    transaction["amount"] = amount_text

            # 11. Additional information (reference, remarks, etc.)
            # Extract column 7 (usually cheque number or reference number)
            col7_cell = row.xpath('.//td[7]')
            col7_text = clean_text(col7_cell[0].text_content()) if col7_cell else ""

            # Extract column 8 (usually remarks or additional information)
            col8_cell = row.xpath('.//td[8]')
            col8_text = clean_text(col8_cell[0].text_content()) if col8_cell else ""

            # Extract column 9 (usually additional remarks)
            col9_cell = row.xpath('.//td[9]')
            col9_text = clean_text(col9_cell[0].text_content()) if col9_cell else ""

            # Also try to extract using specific element IDs as a backup
            # Try to extract cheque number
            cheque_id = f"ContentPlaceHolder1_gedotherbank_lblchequeno_{row_idx}"
            cheque_elements = tree.xpath(f"//span[@id='{cheque_id}']/text()")
            cheque_number = clean_text(cheque_elements[0]) if cheque_elements else ""

            # Try to extract remarks
            remarks_id = f"ContentPlaceHolder1_gedotherbank_lblremarks_{row_idx}"
            remarks_elements = tree.xpath(f"//span[@id='{remarks_id}']/text()")
            remarks = clean_text(remarks_elements[0]) if remarks_elements else ""

            # Try to extract reference number
            ref_id = f"ContentPlaceHolder1_gedotherbank_lblrefno_{row_idx}"
            ref_elements = tree.xpath(f"//span[@id='{ref_id}']/text()")
            ref_number = clean_text(ref_elements[0]) if ref_elements else ""

            # Combine all reference information into a single string
            reference_text = ""

            # Use cheque number if available, otherwise use column 7 text
            if cheque_number:
                reference_text += cheque_number
            elif col7_text:
                reference_text += col7_text

            # Add remarks or column 8 text if available
            if remarks and remarks not in reference_text:
                reference_text += " " + remarks if reference_text else remarks
            elif col8_text and col8_text not in reference_text:
                reference_text += " " + col8_text if reference_text else col8_text

            # Add column 9 text if available and not already included
            if col9_text and col9_text not in reference_text:
                reference_text += " " + col9_text if reference_text else col9_text

            # Add reference number if available and not already included
            if ref_number and ref_number not in reference_text:
                reference_text += " " + ref_number if reference_text else ref_number

            # Set the reference column with the combined information
            transaction["receiver_info"] = reference_text.strip()
            # Keep reference for backward compatibility
            transaction["reference"] = transaction["receiver_info"]

            # Card-specific fields
            # Extract card number if present (masked like XXXX1234)
            card_match = re.search(r'(?:X+|\*+)\d{4}', transaction["reference"])
            if card_match:
                transaction["card_number"] = card_match.group(0)

            # Extract ATM/POS location if present
            txn_type_lower = transaction.get("txn_type", transaction.get("type", "")).lower()
            if "atm" in txn_type_lower or "pos" in txn_type_lower:
                location_match = re.search(r'(?:at|@)\s+([\w\s,.-]+)', transaction["reference"])
                if location_match:
                    transaction["location"] = location_match.group(1).strip()

            # Skip rows with missing critical data
            if not transaction["sender_account"] and not transaction["amount"]:
                logger.warning(f"Skipping row {row_idx} due to missing critical data")
                continue

            # Check if this transaction is a duplicate
            is_duplicate = False
            for existing_txn in transactions:
                if (existing_txn.get("sender_account") == transaction.get("sender_account") and
                    existing_txn.get("receiver_account") == transaction.get("receiver_account") and
                    existing_txn.get("amount") == transaction.get("amount") and
                    existing_txn.get("receiver_transaction_id") == transaction.get("receiver_transaction_id")):
                    is_duplicate = True
                    logger.warning(f"Skipping duplicate transaction for account {transaction.get('sender_account')}")
                    break

            # Only add if not a duplicate
            if not is_duplicate:
                # Log the transaction details for debugging
                logger.info(f"Adding transaction: Layer={transaction.get('layer')}, Sender Account={transaction.get('sender_account')}, Receiver Account={transaction.get('receiver_account')}")
                transactions.append(transaction)

        except Exception as e:
            logger.error(f"Error processing row {row_idx} in Card extraction: {str(e)}")
            continue

    return transactions

def extract_victim_transaction_table(tree, table_id="ContentPlaceHolder1_gv_ActiontakenStatus"):
    """
    Extract transaction data from the victim's transaction table with the specified ID.

    Args:
        tree: HTML tree parsed with lxml
        table_id: ID of the table to extract (default: ContentPlaceHolder1_gv_ActiontakenStatus)

    Returns:
        List of rows from the table, each row is a list of td elements
    """
    # Try to find the table by ID
    table = tree.xpath(f"//table[@id='{table_id}']")

    if not table:
        logger.warning(f"Victim transaction table with ID '{table_id}' not found")
        return []

    # Get all rows from the table, skip the header row
    rows = table[0].xpath('.//tr')[1:] if table else []

    if not rows:
        logger.warning(f"No rows found in victim transaction table with ID '{table_id}'")

    return rows

def extract_transactions(tree, fraud_type=None, max_layer=None):
    """
    Main function to extract all transaction data from the complaint HTML.
    Enhanced to use the new extract_with_custom_logic function.

    Args:
        tree: HTML tree parsed with lxml
        fraud_type: Type of fraud (online_banking or card)
        max_layer: If provided, only return transactions with layer <= max_layer

    Returns:
        Tuple of (all_transactions, layer_transactions)
    """
    # Use the new custom extraction logic function
    extraction_type = fraud_type if fraud_type else "default"
    all_transactions = extract_with_custom_logic(tree, extraction_type, max_layer=max_layer)

    # Process transactions to determine layers
    layer_transactions = {f"Layer {i}": [] for i in range(8)}  # Layers 0-7
    processed_pairs = set()  # To track already processed account+txn pairs

    # First, add transactions to their initial layers
    for txn in all_transactions:
        # Use the layer value directly from the extraction, don't default to 0
        layer_value = txn.get("layer")

        # If layer is None, skip this transaction for now
        if layer_value is None:
            logger.warning(f"Transaction has no layer value: {txn.get('sender_account', '')}")
            continue

        # Convert layer value to int if it's a string
        if isinstance(layer_value, str):
            try:
                layer_value = int(layer_value)
            except (ValueError, TypeError):
                # If conversion fails, log warning and skip this transaction
                logger.warning(f"Invalid layer value: {layer_value}, skipping transaction")
                continue

        layer_key = f"Layer {layer_value}"

        # Add to layer transactions if not already there
        # Check if this is from the ID-based extraction (banking_upi)
        is_id_based = "sender_account" in txn or "receiver_account" in txn

        if is_id_based:
            account_no = txn.get("sender_account", "")
        else:
            account_no = txn.get("account_no", "")

        # Make sure type, amount, and date are handled safely
        txn_type = txn.get('type', '')
        txn_amount = txn.get('amount', '')
        txn_date = txn.get('date', '')

        pair_key = f"{account_no}_{txn_type}_{txn_amount}_{txn_date}"
        if pair_key not in processed_pairs:
            if layer_key in layer_transactions:
                layer_transactions[layer_key].append(txn)
                processed_pairs.add(pair_key)

    # Second pass: Process transactions to determine layers
    for i in range(1, 8):  # Process layers 1 to 7
        prev_layer_key = f"Layer {i-1}"
        curr_layer_key = f"Layer {i}"

        for prev_txn in layer_transactions[prev_layer_key]:
            # For each transaction in previous layer, find related transactions
            for txn in all_transactions:
                # For ID-based extraction, don't modify the layer value
                # Check if this is from the ID-based extraction (banking_upi)
                is_id_based_curr = "sender_account" in txn or "receiver_account" in txn

                # Skip layer assignment for ID-based extraction
                if is_id_based_curr:
                    continue

                # For legacy extraction, continue with the existing logic
                # Skip if already assigned to a layer
                if txn.get("layer") is not None and txn["layer"] > 0 and txn["layer"] < i:
                    continue

                # Check if this transaction is related to the previous layer transaction
                is_related = False

                # Check if this is from the ID-based extraction (banking_upi)
                is_id_based_prev = "sender_account" in prev_txn or "receiver_account" in prev_txn

                # Get account numbers based on extraction type
                prev_account = prev_txn.get("sender_account", "") if is_id_based_prev else prev_txn.get("account_no", "")
                curr_account = txn.get("account_no", "")

                # Get remarks for current transaction
                curr_remarks = txn.get("remarks", "")

                # Case 1: Account numbers match
                if prev_account and curr_account and prev_account == curr_account:
                    is_related = True

                # Case 2: Transaction mentions the previous account
                elif prev_account and curr_remarks and prev_account in curr_remarks:
                    is_related = True

                # Case 3: Transaction mentions the previous account holder (for legacy format)
                elif not is_id_based_prev and prev_txn.get("account_holder") and curr_remarks and prev_txn.get("account_holder") in curr_remarks:
                    is_related = True

                # If related and not already in a lower layer, assign to current layer
                if is_related and (txn.get("layer", 0) == 0 or txn.get("layer", 0) >= i):
                    txn["layer"] = i

                    # Add to current layer if not already there
                    # Check if this is from the ID-based extraction (banking_upi)
                    is_id_based = "sender_account" in txn or "receiver_account" in txn

                    if is_id_based:
                        account_no = txn.get("sender_account", "")
                    else:
                        account_no = txn.get("account_no", "")

                    # Make sure type, amount, and date are handled safely
                    txn_type = txn.get('type', '')
                    txn_amount = txn.get('amount', '')
                    txn_date = txn.get('date', '')

                    pair_key = f"{account_no}_{txn_type}_{txn_amount}_{txn_date}"
                    if pair_key not in processed_pairs:
                        layer_transactions[curr_layer_key].append(txn)
                        processed_pairs.add(pair_key)

    # Sort transactions within each layer by date
    for layer_key in layer_transactions:
        layer_transactions[layer_key].sort(key=lambda x: x.get("date", "") or "")

    return all_transactions, layer_transactions


@router.post("/extract-complaint")
async def extract_complaint(
    request: Request,
    file: UploadFile = File(...),
    max_layer: int = Form(7),  # Default to 7 if not specified, read from FormData
    fraud_type: str = Form(None),  # Optional fraud type for specialized extraction, read from FormData
    extraction_type: str = Form(None),  # Optional extraction type for specialized extraction logic, read from FormData
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """
    Extract data from a complaint HTML file

    Args:
        file: HTML file containing complaint data
        max_layer: Maximum layer to extract and visualize (0-7, default: 7)
        fraud_type: Type of fraud (online_banking or card)
        extraction_type: Specialized extraction logic to use (banking_upi, card, etc.)
        current_user: Current authenticated user
        db: Database connection

    Returns:
        Dictionary containing extracted data, including metadata, transactions,
        layer transactions, bank notice data, graph data, and notice document
    """
    # Log request details for debugging
    import logging
    logger = logging.getLogger(__name__)

    try:
        # Validate inputs
        # Validate max_layer
        is_valid_max_layer, sanitized_max_layer, max_layer_error = validate_numeric(
            max_layer, min_value=0, max_value=10
        )
        if not is_valid_max_layer:
            validation_error("Invalid max_layer value", "max_layer", {"error": max_layer_error})

        # Convert sanitized_max_layer to int for use in functions that require int
        sanitized_max_layer = int(sanitized_max_layer) if sanitized_max_layer is not None else 7

        # Validate fraud_type if provided
        if fraud_type:
            is_valid_fraud_type, sanitized_fraud_type, fraud_type_error = validate_string(
                fraud_type, max_length=50, allow_none=True
            )
            if not is_valid_fraud_type:
                validation_error("Invalid fraud_type", "fraud_type", {"error": fraud_type_error})
        else:
            sanitized_fraud_type = None

        # Validate extraction_type if provided
        if extraction_type:
            is_valid_extraction_type, sanitized_extraction_type, extraction_type_error = validate_string(
                extraction_type, max_length=50, allow_none=True
            )
            if not is_valid_extraction_type:
                validation_error("Invalid extraction_type", "extraction_type", {"error": extraction_type_error})
        else:
            sanitized_extraction_type = None

        # Log request headers
        user_email = current_user.email if hasattr(current_user, 'email') else str(current_user)
        logger.info(f"Extract complaint request received from user: {user_email}")
        logger.info(f"Request headers: {dict(request.headers)}")
        logger.info(f"File name: {file.filename}, Max layer: {sanitized_max_layer}, Fraud type: {sanitized_fraud_type}, Extraction type: {sanitized_extraction_type}")

        # Validate file
        if not file or not file.filename:
            validation_error("Missing file", "file")

        # Check file extension
        if not isinstance(file.filename, str) or not file.filename.lower().endswith('.html'):
            validation_error("Only HTML files are supported", "file")

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            # Write uploaded file content to temp file
            shutil.copyfileobj(file.file, temp_file)
            temp_file_path = temp_file.name

        try:
            # Read the HTML content
            with open(temp_file_path, "r", encoding="utf-8") as f:
                html_content = f.read()

            # Sanitize HTML content (now returns original content for extraction)
            sanitized_html_content = sanitize_html(html_content)

            # Parse HTML
            tree = html.fromstring(sanitized_html_content)

            # Check for critical elements
            main_table = tree.xpath("//table[@id='ContentPlaceHolder1_gedotherbank']")
            logger.info(f"Main transaction table found: {bool(main_table)}")

            victim_table = tree.xpath("//table[@id='ContentPlaceHolder1_gv_ActiontakenStatus']")
            logger.info(f"Victim transaction table found: {bool(victim_table)}")

            date_element = tree.xpath("//*[@id='ContentPlaceHolder1_lblComplaintDate']")
            logger.info(f"Date element found: {bool(date_element)}")

            complainant_name = tree.xpath("//*[@id='ContentPlaceHolder1_lblName']")
            logger.info(f"Complainant name element found: {bool(complainant_name)}")

            # Extract metadata
            metadata = extract_metadata(tree)

            # Check if a complaint with this number already exists
            complaint_number = metadata.get('complaint_number')
            user_id = str(current_user.get("_id", "")) if isinstance(current_user, dict) else ""

            # DEBUG: Log metadata and user info
            logger.info(f"Extracted metadata: {metadata}")
            logger.info(f"User ID for duplicate check: {user_id}")
            logger.info(f"Complaint number for duplicate check: {complaint_number}")

            duplicate_found = False
            duplicate_info = None

            if complaint_number:
                # Check if this complaint already exists for this user
                query = sanitize_find_query({
                    "complaint_number": complaint_number,
                    "user_id": user_id
                })

                # DEBUG: Log the query being used
                logger.info(f"Duplicate check query: {query}")

                existing_complaint = await db.complaints.find_one(query)

                # DEBUG: Log if complaint was found
                if existing_complaint:
                    logger.info(f"Found existing complaint with ID: {existing_complaint.get('_id')}")
                    logger.info(f"Existing complaint data: complaint_number={existing_complaint.get('complaint_number')}, user_id={existing_complaint.get('user_id')}")

                    duplicate_found = True
                    duplicate_info = {
                        "id": str(existing_complaint["_id"]),
                        "complaint_number": complaint_number,
                        "upload_date": existing_complaint.get("upload_date", "Unknown")
                    }

                    # Inform the user about the duplicate
                    logger.info(f"Duplicate complaint found: {complaint_number} for user {user_id}")
                else:
                    logger.info(f"No duplicate complaint found for complaint_number={complaint_number}, user_id={user_id}")
            else:
                logger.warning(f"No complaint number found in metadata, skipping duplicate check")

            # Determine which extraction method to use
            # DEBUG: Log the extraction method selection
            logger.info(f"Selecting extraction method - extraction_type: {sanitized_extraction_type}, fraud_type: {sanitized_fraud_type}")

            if sanitized_extraction_type:
                logger.info(f"Using specialized extraction type: {sanitized_extraction_type}")

                # DEBUG: Check tree structure before extraction
                logger.info(f"Tree structure before extraction - Number of elements: {len(tree.xpath('//*'))}")
                logger.info(f"HTML elements in tree: {[e.tag for e in tree.xpath('//*')[:10]]}...")

                # Use the custom extraction logic with the specified extraction type and max_layer
                all_transactions = extract_with_custom_logic(tree, sanitized_extraction_type, max_layer=sanitized_max_layer)

                # DEBUG: Log the number of transactions extracted
                logger.info(f"Extracted {len(all_transactions)} transactions using {sanitized_extraction_type} extraction")
                if all_transactions:
                    logger.info(f"First transaction sample: {all_transactions[0]}")
                else:
                    logger.warning(f"No transactions extracted using {sanitized_extraction_type} extraction")

                # Process transactions to determine layers
                layer_transactions = {f"Layer {i}": [] for i in range(8)}  # Layers 0-7
                processed_pairs = set()  # To track already processed account+txn pairs

                # Add transactions to their layers
                for txn in all_transactions:
                    layer_value = txn.get("layer", 0)  # Default to layer 0 if not set
                    # Convert layer value to int if it's a string
                    if isinstance(layer_value, str):
                        try:
                            layer_value = int(layer_value)
                        except (ValueError, TypeError):
                            # If conversion fails, log warning and use default layer 0
                            logger.warning(f"Invalid layer value: {layer_value}, using default layer 0")
                            layer_value = 0
                    layer_key = f"Layer {layer_value}"

                    # Add to layer transactions if not already there
                    # Check if this is from the ID-based extraction (banking_upi)
                    is_id_based = "sender_account" in txn or "receiver_account" in txn

                    if is_id_based:
                        account_no = txn.get("sender_account", "")
                    else:
                        account_no = txn.get("account_no", "")

                    pair_key = f"{account_no}_{txn['type']}_{txn['amount']}_{txn.get('date', '')}"
                    if pair_key not in processed_pairs and layer_key in layer_transactions:
                        layer_transactions[layer_key].append(txn)
                        processed_pairs.add(pair_key)

                # DEBUG: Log layer transactions
                for layer, txns in layer_transactions.items():
                    logger.info(f"{layer}: {len(txns)} transactions")
            else:
                # Use the traditional extraction method based on fraud type
                logger.info(f"Using traditional extraction with fraud type: {sanitized_fraud_type}")

                # DEBUG: Check tree structure before extraction
                logger.info(f"Tree structure before traditional extraction - Number of elements: {len(tree.xpath('//*'))}")

                all_transactions, layer_transactions = extract_transactions(tree, sanitized_fraud_type, max_layer=sanitized_max_layer)

                # DEBUG: Log the number of transactions extracted
                logger.info(f"Extracted {len(all_transactions)} transactions using traditional extraction")
                if all_transactions:
                    logger.info(f"First transaction sample: {all_transactions[0]}")
                else:
                    logger.warning("No transactions extracted using traditional extraction")

                # DEBUG: Log layer transactions
                for layer, txns in layer_transactions.items():
                    logger.info(f"{layer}: {len(txns)} transactions")

            # Generate bank notice data
            bank_notice_data = structure_transactions_by_bank(all_transactions)

            # Add fraud type to metadata if provided
            if sanitized_fraud_type:
                metadata['fraud_type'] = sanitized_fraud_type

            # Generate graph data with specified max_layer
            graph_data = generate_graph_data(all_transactions, metadata, sanitized_max_layer)

            # Get user's template if available
            user_template = None
            query = sanitize_find_query({"user_id": user_id})
            template_doc = await db.templates.find_one(query)

            if template_doc and "template_content" in template_doc:
                # Convert base64 template to BytesIO
                try:
                    template_bytes = base64.b64decode(template_doc["template_content"])
                    user_template = BytesIO(template_bytes)
                except Exception as e:
                    logger.error(f"Error decoding template: {str(e)}")
                    user_template = None

            # Generate notice using template or default
            # Use default template if user template is None
            docx_bytes = generate_notice_with_docxtpl(
                bank_notice_data=bank_notice_data,
                metadata=metadata,
                template_content=user_template if user_template is not None else BytesIO()
            )

            # Convert DOCX to base64 for frontend
            docx_base64 = base64.b64encode(docx_bytes.getvalue()).decode('utf-8')

            # Generate CSV from transactions (this will organize them)
            logger.info(f"Generating CSV from {len(all_transactions)} transactions")
            csv_data = generate_csv_from_transactions(all_transactions)
            csv_base64 = csv_to_base64(csv_data)
            logger.info("CSV generation complete")

            # Check if we have victim transactions
            has_victim_transactions = any(txn.get("is_victim_transaction", False) for txn in all_transactions)

            # Check if any transaction has a warning message
            warning_messages = [txn.get("warning") for txn in all_transactions if "warning" in txn]
            has_warnings = len(warning_messages) > 0
            warning_message = warning_messages[0] if has_warnings else None

            # Return the extracted data with duplicate info if found
            response_data = {
                "metadata": metadata,
                "transactions": all_transactions,
                "layer_transactions": layer_transactions,
                "bank_notice_data": bank_notice_data,  # This is now in the bank-structured format
                "graph_data": graph_data,
                "notice_docx_base64": docx_base64,
                "csv_data_base64": csv_base64,  # Add CSV data
                "max_layer": sanitized_max_layer,  # Include the max_layer value in the response
                "has_victim_transactions": has_victim_transactions,  # Flag indicating if victim transactions were used
                "extraction_type": sanitized_extraction_type,  # Include the extraction type used
                "has_warnings": has_warnings,  # Flag indicating if there are any warnings
                "warning_message": warning_message  # Include the warning message if any
            }

            # Add duplicate information if found
            if duplicate_found:
                response_data["duplicate_found"] = True
                response_data["duplicate_info"] = duplicate_info

            return response_data

        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(f"Error processing file: {str(e)}")
            server_error(f"Error processing file: {str(e)}")

        finally:
            # Clean up the temp file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error in extract_complaint: {str(e)}")
        server_error(f"An unexpected error occurred during complaint extraction")

class CSVData(BaseModel):
    csv_data_base64: str
    metadata: Optional[Dict[str, Any]] = None

@router.post("/parse-csv")
async def parse_csv_data(
    data: CSVData,
    complaint_id: str = "",
    current_user: dict = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """
    Parse CSV data and generate graph data
    """
    logger.info(f"parse-csv endpoint called for complaint_id: {complaint_id}")
    logger.info(f"CSV data length: {len(data.csv_data_base64) if data.csv_data_base64 else 0}")
    logger.info(f"Metadata provided: {bool(data.metadata)}")

    if data.metadata:
        logger.info(f"Metadata keys: {list(data.metadata.keys())}")

    try:
        # Validate input data
        if not data:
            logger.error("No data provided")
            raise HTTPException(status_code=400, detail="No data provided")

        if not data.csv_data_base64 or not data.csv_data_base64.strip():
            logger.error("Empty CSV data provided")
            raise HTTPException(status_code=400, detail="Empty CSV data provided")

        # Validate complaint_id if provided
        if complaint_id:
            try:
                # Check if complaint_id is a valid ObjectId
                try:
                    from bson.objectid import ObjectId
                    complaint_obj_id = ObjectId(complaint_id)
                except:
                    logger.error(f"Invalid complaint ID format: {complaint_id}")
                    raise HTTPException(status_code=400, detail="Invalid complaint ID format")

                # Check if complaint exists and belongs to the current user
                if db:
                    complaint = await db.complaints.find_one({
                        "_id": complaint_obj_id,
                        "user_id": str(current_user.get("_id", ""))
                    })

                    if not complaint:
                        logger.warning(f"Complaint not found or not owned by user: {complaint_id}")
                        raise HTTPException(status_code=404, detail="Complaint not found or not owned by user")
            except HTTPException:
                # Re-raise HTTP exceptions
                raise
            except Exception as e:
                logger.error(f"Error validating complaint ID: {str(e)}")
                raise HTTPException(status_code=400, detail="Invalid complaint ID format")

        # Log the first 100 characters of the CSV data for debugging
        logger.info(f"Received CSV data (first 100 chars): {data.csv_data_base64[:100]}...")
        logger.info(f"CSV data length: {len(data.csv_data_base64)}")

        # Use the graph service to process CSV data
        from services.graph_service import process_csv_data

        # Use provided metadata if available
        metadata = data.metadata

        # Find the complaint if complaint_id is provided
        complaint = None
        if complaint_id:
            try:
                # Try to find the complaint by ID or complaint number
                try:
                    from bson.objectid import ObjectId
                    user_id = str(current_user.get("_id", "")) if isinstance(current_user, dict) else ""
                    query = {"_id": ObjectId(complaint_id), "user_id": user_id}
                except:
                    user_id = str(current_user.get("_id", "")) if isinstance(current_user, dict) else ""
                    query = {"complaint_number": complaint_id, "user_id": user_id}

                complaint = await db.complaints.find_one(query)
                if complaint:
                    # Decrypt the complaint to get proper values
                    from services.encryption_service import decrypt_complaint
                    decrypted_complaint = decrypt_complaint(complaint)
                    logger.info(f"Decrypted complaint data for metadata extraction")

                    # First try to use the existing graph_data metadata if available
                    if "graph_data" in complaint and complaint["graph_data"] and "metadata" in complaint["graph_data"]:
                        metadata = complaint["graph_data"]["metadata"]
                        logger.info(f"Using metadata from graph_data in complaint {complaint_id}")

                        # Process complainant_name if it might be encrypted
                        if "complainant_name" in metadata and metadata["complainant_name"]:
                            try:
                                from services.notice_service import process_complainant_name
                                metadata["complainant_name"] = process_complainant_name(metadata)
                                logger.info(f"Processed complainant name in graph metadata")
                            except Exception as e:
                                logger.error(f"Error processing complainant name in graph metadata: {e}")

                    # Next try to use the metadata field if available
                    elif "metadata" in complaint and complaint["metadata"]:
                        metadata = complaint["metadata"]
                        logger.info(f"Using metadata from metadata field in complaint {complaint_id}")
                    # Finally, create metadata from complaint fields
                    else:
                        metadata = {
                            "complainant_name": decrypted_complaint.get("complainant_name", ""),
                            "complaint_number": decrypted_complaint.get("complaint_number", ""),
                            "total_amount": decrypted_complaint.get("amount", "")
                        }
                        logger.info(f"Created metadata from complaint {complaint_id} fields")

                    # Always use the original complaint date from original_complaint_date if available
                    if decrypted_complaint.get("original_complaint_date"):
                        metadata["date"] = decrypted_complaint.get("original_complaint_date")
                        logger.info(f"Using original extracted complaint date in metadata: {metadata['date']}")
                    # Fallback to date field if original_complaint_date is not available
                    elif decrypted_complaint.get("date") and not metadata.get("date"):
                        complaint_date = decrypted_complaint.get("date")
                        if isinstance(complaint_date, datetime.datetime):
                            formatted_date = complaint_date.strftime('%d/%m/%Y')
                            metadata["date"] = formatted_date
                        else:
                            metadata["date"] = str(complaint_date)
                        logger.info(f"Using date field as fallback in metadata: {metadata['date']}")
            except Exception as e:
                logger.warning(f"Error getting complaint metadata: {str(e)}")
                # Continue with default metadata

        # If no metadata was found, use default values
        if metadata is None:
            metadata = {
                "complainant_name": "CSV Import",
                "complaint_number": "CSV-IMPORT",
                "date": "",
                "total_amount": "",
                # Explicitly set empty address fields for CSV imports
                "address": {
                    "house_no": "",
                    "street": "",
                    "colony": "",
                    "village_town_city": "",
                    "tehsil": "",
                    "district": "",
                    "state": "",
                    "country": "",
                    "police_station": "",
                    "pin_code": ""
                }
            }
            logger.info("Using default metadata values with empty address fields")

        # Process CSV data
        try:
            # Process the CSV data to extract transactions and generate graph data
            transactions, graph_data = process_csv_data(data.csv_data_base64, metadata)

            if not transactions:
                logger.error("No transactions found in CSV data")
                raise HTTPException(status_code=400, detail="No transactions found in CSV data")

            logger.info(f"Processed {len(transactions)} transactions from CSV data")

            # Generate bank notice data
            bank_notice_data = structure_transactions_by_bank(transactions)

            # Generate CSV data (this will organize the transactions)
            csv_output = generate_csv_from_transactions(transactions)

            # Ensure the CSV data is properly formatted
            # First, check if the CSV output is a StringIO object
            if isinstance(csv_output, StringIO):
                # Get the string value and encode it to bytes
                csv_bytes = csv_output.getvalue().encode('utf-8')
            else:
                # If it's already a string, encode it to bytes
                csv_bytes = csv_output.encode('utf-8') if isinstance(csv_output, str) else csv_output

            # Convert to base64
            csv_base64 = base64.b64encode(csv_bytes).decode('utf-8')

            logger.info(f"Generated CSV data with {len(csv_base64)} characters")

            # Check if we have victim transactions
            has_victim_transactions = any(txn.get("is_victim_transaction", False) for txn in transactions)

            # If we have a complaint, update it with the new data
            if complaint and complaint_id:
                try:
                    # First, preserve the original metadata from the complaint
                    original_metadata = None
                    if "graph_data" in complaint and complaint["graph_data"] and "metadata" in complaint["graph_data"]:
                        original_metadata = complaint["graph_data"]["metadata"]
                        logger.info(f"Preserved original metadata from complaint before update")

                    # Update the complaint with the new data
                    update_data = {
                        "transactions": transactions,
                        "csv_data_base64": csv_base64,
                        "bank_notice_data": bank_notice_data
                    }

                    # ALWAYS preserve the original complaint date - this is critical
                    # First check for original_complaint_date which should never change
                    if "original_complaint_date" in complaint and complaint["original_complaint_date"]:
                        update_data["original_complaint_date"] = complaint["original_complaint_date"]
                        # Also set the date field to match the original date to ensure consistency
                        update_data["date"] = complaint["original_complaint_date"]
                        # Make sure metadata has the date too
                        if "metadata" not in update_data:
                            update_data["metadata"] = {}
                        update_data["metadata"]["date"] = complaint["original_complaint_date"]
                        logger.info(f"Preserved original_complaint_date during CSV update: {complaint['original_complaint_date']}")
                    # If no original_complaint_date, fall back to date field
                    elif "date" in complaint and complaint["date"]:
                        update_data["date"] = complaint["date"]
                        # Also set original_complaint_date to match to ensure future consistency
                        update_data["original_complaint_date"] = complaint["date"]
                        # Make sure metadata has the date too
                        if "metadata" not in update_data:
                            update_data["metadata"] = {}
                        update_data["metadata"]["date"] = complaint["date"]
                        logger.info(f"Set original_complaint_date from date field during CSV update: {complaint['date']}")

                    # If we have original metadata, make sure it's preserved in the graph_data
                    if original_metadata and "metadata" in graph_data:
                        # Create a new graph_data object with the original metadata
                        # Make a copy to avoid modifying the original
                        merged_metadata = {**graph_data["metadata"]}

                        # Preserve important fields from original metadata
                        if "complaint_number" in original_metadata:
                            merged_metadata["complaint_number"] = original_metadata["complaint_number"]
                        if "complainant_name" in original_metadata:
                            merged_metadata["complainant_name"] = original_metadata["complainant_name"]

                        # ALWAYS preserve the original date from metadata
                        # This is critical to prevent transaction dates from overwriting the original complaint date
                        if "date" in original_metadata:
                            merged_metadata["date"] = original_metadata["date"]
                            logger.info(f"Preserved original date from metadata: {original_metadata['date']}")

                        # Update the graph data with merged metadata
                        graph_data["metadata"] = merged_metadata
                        logger.info(f"Restored original metadata in graph_data")

                    # Now add the graph_data to the update
                    update_data["graph_data"] = graph_data

                    # Make sure we're preserving the original metadata in the graph_data
                    if "graph_data" in update_data and update_data["graph_data"] and "metadata" in update_data["graph_data"]:
                        # Check if we have the original metadata
                        if "graph_data" in complaint and complaint["graph_data"] and "metadata" in complaint["graph_data"]:
                            original_metadata = complaint["graph_data"]["metadata"]

                            # Get decrypted complaint data
                            from services.encryption_service import decrypt_complaint
                            decrypted_complaint = decrypt_complaint(complaint)

                            # Create a merged metadata that preserves important fields
                            merged_metadata = update_data["graph_data"]["metadata"].copy()

                            # Preserve original complainant_name if it's not the default
                            if original_metadata.get("complainant_name") != "CSV Import":
                                merged_metadata["complainant_name"] = original_metadata.get("complainant_name")
                                logger.info(f"Preserved original complainant_name in metadata")
                            elif decrypted_complaint.get("complainant_name") and decrypted_complaint.get("complainant_name") != "CSV Import":
                                merged_metadata["complainant_name"] = decrypted_complaint.get("complainant_name")
                                logger.info(f"Used decrypted complainant_name in metadata")

                            # Preserve original complaint_number if it's not the default
                            if original_metadata.get("complaint_number") != "CSV-IMPORT":
                                merged_metadata["complaint_number"] = original_metadata.get("complaint_number")
                                logger.info(f"Preserved original complaint_number in metadata")
                            elif decrypted_complaint.get("complaint_number"):
                                merged_metadata["complaint_number"] = decrypted_complaint.get("complaint_number")
                                logger.info(f"Used decrypted complaint_number in metadata")

                            # ALWAYS use the original complaint date from the original_complaint_date field
                            # This is the most reliable source of the original date
                            if decrypted_complaint.get("original_complaint_date"):
                                # Store the original date in the metadata
                                merged_metadata["date"] = decrypted_complaint.get("original_complaint_date")
                                logger.info(f"Using original_complaint_date in metadata: {merged_metadata['date']}")

                                # Also ensure the date is preserved in the update_data
                                update_data["original_complaint_date"] = decrypted_complaint.get("original_complaint_date")
                                update_data["date"] = decrypted_complaint.get("original_complaint_date")
                                # Make sure metadata has the date too
                                if "metadata" not in update_data:
                                    update_data["metadata"] = {}
                                update_data["metadata"]["date"] = decrypted_complaint.get("original_complaint_date")
                                logger.info(f"Preserved original date in update_data fields")
                            # Fallback to original metadata date if available
                            elif original_metadata.get("date"):
                                merged_metadata["date"] = original_metadata.get("date")
                                logger.info(f"Using date from original metadata: {merged_metadata['date']}")

                                # Also ensure the date is preserved in the update_data
                                update_data["original_complaint_date"] = original_metadata.get("date")
                                update_data["date"] = original_metadata.get("date")
                                # Make sure metadata has the date too
                                if "metadata" not in update_data:
                                    update_data["metadata"] = {}
                                update_data["metadata"]["date"] = original_metadata.get("date")
                                logger.info(f"Set date fields from original metadata")
                            # Last fallback to date field
                            elif decrypted_complaint.get("date"):
                                complaint_date = decrypted_complaint.get("date")
                                if isinstance(complaint_date, datetime.datetime):
                                    formatted_date = complaint_date.strftime('%d/%m/%Y')
                                    merged_metadata["date"] = formatted_date
                                else:
                                    merged_metadata["date"] = str(complaint_date)
                                logger.info(f"Using date field as fallback in metadata: {merged_metadata['date']}")

                                # Also ensure the date is preserved in the update_data
                                update_data["original_complaint_date"] = merged_metadata["date"]
                                update_data["date"] = merged_metadata["date"]
                                # Make sure metadata has the date too
                                if "metadata" not in update_data:
                                    update_data["metadata"] = {}
                                update_data["metadata"]["date"] = merged_metadata["date"]
                                logger.info(f"Set date fields from fallback date")

                            # Preserve total_amount if available
                            if original_metadata.get("total_amount"):
                                merged_metadata["total_amount"] = original_metadata.get("total_amount")
                            elif decrypted_complaint.get("amount"):
                                merged_metadata["total_amount"] = decrypted_complaint.get("amount")

                            # Update the metadata in the graph_data
                            logger.info(f"Using merged metadata in graph_data for complaint {complaint_id}")
                            update_data["graph_data"]["metadata"] = merged_metadata

                    # Update the complaint in the database
                    result = await db.complaints.update_one(
                        {"_id": complaint["_id"]},
                        {"$set": update_data}
                    )

                    if result.modified_count > 0:
                        logger.info(f"Updated complaint {complaint_id} with new CSV data")
                    else:
                        logger.warning(f"No changes made to complaint {complaint_id}")
                except Exception as e:
                    logger.error(f"Error updating complaint with new CSV data: {str(e)}")
                    # Continue without updating the complaint

            # Return the processed data
            return {
                "transactions": transactions,
                "graph_data": graph_data,
                "bank_notice_data": bank_notice_data,
                "csv_data_base64": csv_base64,
                "notice_docx_base64": None,  # We don't generate notice here
                "has_victim_transactions": has_victim_transactions
            }
        except ValueError as ve:
            logger.error(f"Error processing CSV data: {str(ve)}")
            raise HTTPException(status_code=400, detail=f"Error processing CSV data: {str(ve)}")

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error parsing CSV data: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Unexpected error parsing CSV data: {str(e)}")

class TableRowOperation(BaseModel):
    complaint_id: str
    operation: str  # "update" or "delete"
    row_id: str
    csv_data_base64: str
    metadata: Optional[Dict[str, Any]] = None  # Optional metadata to preserve during operations

@router.post("/table-row-operation")
async def handle_table_row_operation(
    data: TableRowOperation,
    current_user: dict = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """
    Handle table row operations (update or delete)

    This endpoint is specifically designed to handle individual row operations
    in the transaction table, ensuring they are properly persisted to the database.
    It only updates the specific parts of the data that changed, preserving metadata.
    """
    try:
        logger.info(f"Table row operation: {data.operation} for row {data.row_id} in complaint {data.complaint_id}")

        # Validate the operation
        if data.operation not in ["update", "delete"]:
            logger.error(f"Invalid operation: {data.operation}")
            raise HTTPException(status_code=400, detail="Invalid operation. Must be 'update' or 'delete'")

        # Validate the complaint ID
        if not data.complaint_id:
            logger.error("Missing complaint ID")
            raise HTTPException(status_code=400, detail="Missing complaint ID")

        # Verify resource ownership
        try:
            # Check if complaint_id is a valid ObjectId
            from bson.objectid import ObjectId
            user_id = str(current_user.get("_id", ""))

            try:
                complaint_obj_id = ObjectId(data.complaint_id)
                query = {"_id": complaint_obj_id, "user_id": user_id}
            except:
                # If not a valid ObjectId, try as complaint number
                query = {"complaint_number": data.complaint_id, "user_id": user_id}

            complaint = await db.complaints.find_one(query)

            if not complaint:
                logger.warning(f"Complaint not found or not owned by user: {data.complaint_id}")
                raise HTTPException(status_code=404, detail="Complaint not found or not owned by user")

            logger.info(f"Found complaint: {complaint.get('complaint_number', 'Unknown')}")
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error validating complaint ID: {str(e)}")
            raise HTTPException(status_code=400, detail="Invalid complaint ID format")

        # Process the CSV data
        if not data.csv_data_base64:
            logger.error("Empty CSV data provided")
            raise HTTPException(status_code=400, detail="Empty CSV data provided")

        # Extract metadata from the complaint to preserve it
        # This is critical to ensure metadata isn't lost during row operations
        metadata = None

        # First, check if we already have metadata in the request
        # This is the most efficient approach as it avoids decryption and extraction
        if hasattr(data, 'metadata') and data.metadata:
            metadata = data.metadata
            logger.info(f"Using metadata provided in the request: {list(metadata.keys())}")
        else:
            try:
                # Decrypt the complaint to get proper values
                from services.encryption_service import decrypt_complaint
                decrypted_complaint = decrypt_complaint(complaint)
                logger.info(f"Decrypted complaint data for metadata extraction in table-row-operation")

                # First try to use the existing graph_data metadata if available
                if "graph_data" in complaint and complaint["graph_data"]:
                    if isinstance(complaint["graph_data"], str):
                        import json
                        graph_data = json.loads(complaint["graph_data"])
                    else:
                        graph_data = complaint["graph_data"]

                    if "metadata" in graph_data:
                        metadata = graph_data["metadata"]
                        logger.info(f"Using metadata from graph_data in table-row-operation: {list(metadata.keys())}")

                # Next try to use the metadata field if available
                elif "metadata" in complaint and complaint["metadata"]:
                    metadata = complaint["metadata"]
                    logger.info(f"Using metadata from metadata field in table-row-operation")

                # Next try to use the summary field if available
                elif "summary" in complaint and complaint["summary"]:
                    metadata = complaint["summary"]
                    logger.info(f"Using metadata from summary field in table-row-operation")

                # Finally, create metadata from complaint fields
                else:
                    metadata = {
                        "complainant_name": decrypted_complaint.get("complainant_name", ""),
                        "complaint_number": decrypted_complaint.get("complaint_number", ""),
                        "total_amount": decrypted_complaint.get("amount", ""),
                        "date": decrypted_complaint.get("date", "")
                    }
                    logger.info(f"Created metadata from complaint fields in table-row-operation")
            except Exception as e:
                logger.error(f"Error extracting metadata in table-row-operation: {str(e)}")
                # Continue with default metadata

        # Import the process_csv_data function to handle CSV processing
        from services.graph_service import process_csv_data

        # Process the CSV data to extract transactions
        transactions, graph_data = process_csv_data(data.csv_data_base64, metadata)

        if not transactions:
            logger.error("No transactions found in CSV data")
            raise HTTPException(status_code=400, detail="No transactions found in CSV data")

        logger.info(f"Processed {len(transactions)} transactions from CSV data")

        # Generate bank notice data
        from services.notice_service import structure_transactions_by_bank
        bank_notice_data = structure_transactions_by_bank(transactions)

        # Create update data with only the necessary fields
        update_data = {
            "transactions": transactions,
            "csv_data_base64": data.csv_data_base64,
            "bank_notice_data": bank_notice_data
        }

        # Only update graph_data if it's different from what's already in the complaint
        # This preserves any custom graph layouts or settings
        if "graph_data" in complaint and complaint["graph_data"]:
            # Ensure we're comparing objects, not strings
            existing_graph_data = complaint["graph_data"]
            if isinstance(existing_graph_data, str):
                import json
                existing_graph_data = json.loads(existing_graph_data)

            # Only update the transactions in graph_data, preserve metadata and other fields
            if "transactions" in existing_graph_data:
                existing_graph_data["transactions"] = graph_data["transactions"]
                update_data["graph_data"] = existing_graph_data
                logger.info("Updated only transactions in existing graph_data")
            else:
                update_data["graph_data"] = graph_data
                logger.info("Using new graph_data as existing graph_data has no transactions")
        else:
            update_data["graph_data"] = graph_data
            logger.info("No existing graph_data, using new graph_data")

        # CRITICAL: Preserve original complaint date
        # This ensures the date field is never overwritten by transaction dates
        if "original_complaint_date" in complaint and complaint["original_complaint_date"]:
            update_data["original_complaint_date"] = complaint["original_complaint_date"]
            update_data["date"] = complaint["original_complaint_date"]
            logger.info(f"Preserved original_complaint_date: {complaint['original_complaint_date']}")
        elif "date" in complaint and complaint["date"]:
            update_data["date"] = complaint["date"]
            update_data["original_complaint_date"] = complaint["date"]
            logger.info(f"Set original_complaint_date from date field: {complaint['date']}")

        # Update the complaint in the database
        try:
            result = await db.complaints.update_one(
                {"_id": complaint["_id"]},
                {"$set": update_data}
            )

            if result.modified_count > 0:
                logger.info(f"Updated complaint {data.complaint_id} with new CSV data")
            else:
                logger.warning(f"No changes made to complaint {data.complaint_id}")
        except Exception as e:
            logger.error(f"Error updating complaint with new CSV data: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error updating complaint: {str(e)}")

        # Return success response with the processed data
        return {
            "success": True,
            "message": f"Row {data.operation} operation completed successfully",
            "data": {
                "transactions": transactions,
                "graph_data": graph_data,
                "bank_notice_data": bank_notice_data,
                "csv_data_base64": data.csv_data_base64
            }
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error handling table row operation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error handling table row operation: {str(e)}")

@router.get("/download-csv/{complaint_id}")
async def download_csv(
    complaint_id: str,
    format: str = "excel",  # Default to Excel format
    user: Dict[str, Any] = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """
    Download CSV/Excel file for a complaint

    Args:
        complaint_id: ID of the complaint
        format: Format of the file (csv or excel)
        user: Current authenticated user
        db: Database connection

    Returns:
        CSV/Excel file as a streaming response
    """
    try:
        # Find the complaint
        query = {}
        # Extract user ID from user object
        user_id = None
        if isinstance(user, dict):
            from bson.objectid import ObjectId
            user_id = user.get("id") or user.get("_id")
            if user_id and isinstance(user_id, ObjectId):
                user_id = str(user_id)

        logger.info(f"User ID for CSV download: {user_id}")

        try:
            # Check if complaint_id is a valid ObjectId
            from bson.objectid import ObjectId
            query = {"_id": ObjectId(complaint_id), "user_id": user_id}
        except:
            # If not, try to find by complaint_number
            query = {"complaint_number": complaint_id, "user_id": user_id}

        logger.info(f"Looking for complaint with query: {query}")

        complaint = await db.complaints.find_one(query)

        if not complaint:
            raise HTTPException(status_code=404, detail="Complaint not found or unauthorized access")

        # Always prioritize generating CSV from transactions if available
        if "transactions" in complaint and complaint["transactions"]:
            logger.info(f"Generating CSV from {len(complaint['transactions'])} transactions")
            # This will organize the transactions
            csv_data = generate_csv_from_transactions(complaint["transactions"])
            logger.info("CSV generation complete")
        elif "csv_data_base64" in complaint and complaint["csv_data_base64"]:
            # Decode base64 CSV data
            try:
                csv_bytes = base64.b64decode(complaint["csv_data_base64"])
                csv_text = csv_bytes.decode('utf-8')

                # Clean up any metadata at the top of the CSV
                lines = csv_text.split('\n')

                # If there are lines that look like base64 or dates at the top, remove them
                if lines and (lines[0].startswith('Z0') or
                           re.match(r'^\d{1,2}/\d{1,2}/\d{4}$', lines[0]) or
                           not ',' in lines[0]):
                    # Find the first line that looks like a CSV header
                    header_index = next((i for i, line in enumerate(lines)
                                      if ',' in line and line.startswith('Layer')), 0)

                    if header_index > 0:
                        csv_text = '\n'.join(lines[header_index:])

                csv_data = StringIO(csv_text)
            except Exception as e:
                logger.error(f"Error processing CSV data: {str(e)}")
                # Fall back to empty CSV with headers
                csv_data = generate_csv_from_transactions([])
        else:
            raise HTTPException(status_code=404, detail="No transaction data available for this complaint")

        # Check if we should return Excel format
        if format.lower() == "excel":
            import pandas as pd
            from io import BytesIO

            # Convert CSV to DataFrame
            csv_data.seek(0)  # Reset position to start of file
            df = pd.read_csv(csv_data)

            # Create Excel file in memory
            excel_buffer = BytesIO()
            with pd.ExcelWriter(excel_buffer, engine='xlsxwriter') as writer:
                df.to_excel(writer, sheet_name='Transactions', index=False)

                # Create a header format
                try:
                    # Get the xlsxwriter workbook object
                    # Create a format for the header
                    # Note: The IDE might show an error about add_format, but this is correct for xlsxwriter
                    # Type ignore is needed because the IDE doesn't recognize the add_format method
                    header_format = writer.book.add_format({  # type: ignore
                        'bold': True,  # Bold font
                        'text_wrap': True,
                        'valign': 'top',
                        'fg_color': '#D7E4BC',
                        'border': 1
                    })
                except Exception as e:
                    # If there's an error with the formatting, log it and continue without formatting
                    logger.warning(f"Error creating Excel format: {e}")
                    header_format = None

                # Write the column headers with the defined format
                for col_num, value in enumerate(df.columns.values):
                    if header_format is not None:
                        writer.sheets['Transactions'].write(0, col_num, value, header_format)
                    else:
                        writer.sheets['Transactions'].write(0, col_num, value)

                # Set column widths
                writer.sheets['Transactions'].set_column('A:A', 5)  # Layer
                writer.sheets['Transactions'].set_column('B:B', 20)  # Sender Account
                writer.sheets['Transactions'].set_column('C:C', 15)  # Sender Txn ID
                writer.sheets['Transactions'].set_column('D:D', 15)  # Sender Bank
                writer.sheets['Transactions'].set_column('E:E', 20)  # Receiver Account
                writer.sheets['Transactions'].set_column('F:F', 15)  # Txn ID
                writer.sheets['Transactions'].set_column('G:G', 15)  # Receiver Bank
                writer.sheets['Transactions'].set_column('H:H', 15)  # Txn Type
                writer.sheets['Transactions'].set_column('I:I', 12)  # Txn Date
                writer.sheets['Transactions'].set_column('J:J', 12)  # Amount
                writer.sheets['Transactions'].set_column('K:K', 30)  # Reference

            # Return Excel file
            excel_buffer.seek(0)
            return Response(
                content=excel_buffer.getvalue(),
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                headers={
                    "Content-Disposition": f"attachment; filename=Complaint_{complaint.get('complaint_number', 'Unknown')}.xlsx"
                }
            )
        else:
            # Return CSV file
            return Response(
                content=csv_data.getvalue(),
                media_type="text/csv",
                headers={
                    "Content-Disposition": f"attachment; filename=Complaint_{complaint.get('complaint_number', 'Unknown')}.csv"
                }
            )
    except Exception as e:
        logger.error(f"Error downloading file: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error downloading file: {str(e)}")



@router.get("/complaint-summary/{complaint_id}", response_model=Dict[str, Any])
async def get_complaint_summary(
    complaint_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorClient = Depends(get_db)
):
    """
    Get a simple summary of a complaint using only metadata from graph_data.

    Args:
        complaint_id: ID or complaint number of the complaint
        current_user: Current authenticated user
        db: Database connection

    Returns:
        Dictionary containing basic summary information from metadata
    """
    # Log the entry point to help debug
    logger.info(f"ENTRY POINT: get_complaint_summary for complaint_id: {complaint_id}")

    try:
        # Extract user ID safely
        user_id = ""
        try:
            if isinstance(current_user, dict):
                user_id = str(current_user.get("_id", ""))
            elif hasattr(current_user, "model_dump"):
                user_dict = current_user.model_dump()
                user_id = str(user_dict.get("id", user_dict.get("_id", "")))
            else:
                # Try to get the ID using getattr
                for id_attr in ["id", "_id"]:
                    if hasattr(current_user, id_attr):
                        user_id = str(getattr(current_user, id_attr, ""))
                        if user_id:
                            break
        except Exception as e:
            logger.error(f"Error extracting user ID: {str(e)}")

        # Try to find by ID first
        query = {}
        try:
            # Check if complaint_id is a valid ObjectId
            from bson.objectid import ObjectId
            query = {"_id": ObjectId(complaint_id), "user_id": user_id}
        except:
            # If not, try to find by complaint_number
            query = {"complaint_number": complaint_id, "user_id": user_id}

        complaint = await db.complaints.find_one(query)

        if not complaint:
            raise HTTPException(status_code=404, detail="Complaint not found or unauthorized access")

        # Decrypt the complaint to get decrypted fields
        from services.encryption_service import decrypt_complaint
        decrypted_complaint = decrypt_complaint(complaint)

        # Don't try to decrypt the email - just use it as is
        # The email is already extracted correctly from the HTML using the ID "ContentPlaceHolder1_lblEmailId"
        # and we want to display it as-is in the UI
        logger.info(f"Using email as-is without decryption attempt")

        # Get metadata from graph_data if available
        metadata = None
        if "graph_data" in complaint and complaint["graph_data"]:
            try:
                if isinstance(complaint["graph_data"], str):
                    import json
                    graph_data = json.loads(complaint["graph_data"])
                else:
                    graph_data = complaint["graph_data"]

                if "metadata" in graph_data:
                    metadata = graph_data["metadata"]
                    logger.info(f"Found metadata in graph_data: {metadata.keys() if metadata else 'None'}")
            except Exception as e:
                logger.error(f"Error parsing graph_data: {str(e)}")

        # If no metadata found, return basic info
        if not metadata:
            return {
                "complaint_details": {
                    "complaint_number": decrypted_complaint.get("complaint_number", ""),
                    "date": decrypted_complaint.get("date", ""),
                    "category": decrypted_complaint.get("category", ""),
                    "subcategory": decrypted_complaint.get("subcategory", ""),
                    "status": decrypted_complaint.get("status", "Pending")
                },
                "complainant_details": {
                    "name": decrypted_complaint.get("complainant_name", "Unknown"),
                    "mobile": decrypted_complaint.get("complainant_phone", ""),
                    "email": decrypted_complaint.get("complainant_email", ""),
                    "relationship": "",
                    "gender": "",
                    "address": {
                        "house_no": "",
                        "street": "",
                        "colony": "",
                        "village_town_city": "",
                        "tehsil": "",
                        "district": "",
                        "state": "",
                        "country": "",
                        "police_station": "",
                        "pin_code": ""
                    }
                },
                "financial_details": {
                    "total_fraudulent_amount": decrypted_complaint.get("amount", 0),
                    "total_lien_amount": 0
                },
                "notes": decrypted_complaint.get("notes", "")
            }

        # Use consolidated metadata to create a simple summary (no separate summary object)
        # All complainant and financial details are now part of metadata
        return {
            "complaint_details": {
                "complaint_number": metadata.get("complaint_number", decrypted_complaint.get("complaint_number", "")),
                "date": metadata.get("date", decrypted_complaint.get("date", "")),
                "category": metadata.get("category", decrypted_complaint.get("category", "")),
                "subcategory": metadata.get("subcategory", decrypted_complaint.get("subcategory", "")),
                "status": metadata.get("status", decrypted_complaint.get("status", "Pending"))
            },
            "complainant_details": {
                "name": metadata.get("complainant_name", decrypted_complaint.get("complainant_name", "Unknown")),
                "mobile": metadata.get("complainant_mobile", decrypted_complaint.get("complainant_phone", "")),
                "email": metadata.get("complainant_email", decrypted_complaint.get("complainant_email", "")),
                "relationship": metadata.get("complainant_relationship", ""),
                "gender": metadata.get("complainant_gender", ""),
                "address": metadata.get("address", {
                    "house_no": "",
                    "street": "",
                    "colony": "",
                    "village_town_city": "",
                    "tehsil": "",
                    "district": "",
                    "state": "",
                    "country": "",
                    "police_station": "",
                    "pin_code": ""
                })
            },
            "financial_details": {
                "total_fraudulent_amount": metadata.get("total_fraudulent_amount", metadata.get("total_amount", metadata.get("amount", decrypted_complaint.get("amount", 0)))),
                "total_lien_amount": metadata.get("lien_amount", 0),
                "transaction_totals": metadata.get("transaction_totals", {})
            },
            "notes": decrypted_complaint.get("notes", "")
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error getting complaint summary: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting complaint summary: {str(e)}")
