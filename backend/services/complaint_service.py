import logging
from bson import ObjectId
from motor.motor_asyncio import Async<PERSON>MotorClient
from fastapi import HTTP<PERSON>x<PERSON>, UploadFile
from models import Complaint
import datetime
from typing import Optional, Dict, Any
from bs4 import BeautifulSoup
import re
import os
from tempfile import NamedTemporaryFile
from services.encryption_service import encrypt_complaint, decrypt_complaint

# Configure logging
logger = logging.getLogger(__name__)

async def process_complaint_file(
    file: UploadFile,
    file_content: bytes,
    title: Optional[str],
    description: Optional[str],
    user_id: str,
    db: AsyncIOMotorClient
) -> Dict[str, Any]:
    """
    Process uploaded complaint file (HTML or PDF) and extract information

    Args:
        file: The uploaded file
        file_content: The file content as bytes
        title: Optional title for the complaint
        description: Optional description for the complaint
        user_id: ID of the user uploading the complaint
        db: Database connection

    Returns:
        Dictionary with extracted complaint data
    """
    try:
        filename = file.filename.lower() if file.filename else ""

        # Generate a unique complaint number
        complaint_count = await db.complaints.count_documents({})
        complaint_number = f"COMP-{complaint_count + 1:06d}"

        # Initialize complaint data with defaults
        complaint_data = {
            "complaint_number": complaint_number,
            "title": title or f"Complaint {complaint_number}",
            "description": description or "",
            "user_id": user_id,
            "file_name": file.filename,
            "file_type": file.content_type,
            "file_size": len(file_content),
            "upload_date": datetime.datetime.now(),
            "status": "Pending",
            "category": "General",
            "amount": 0.0,
            "complainant_name": "Unknown",  # Default value for the model
            "respondent_name": "",
            "extracted_text": "",
            "metadata": {},
            "date": None  # Will be updated with the extracted date from HTML
        }

        # Process based on file type
        if filename.endswith('.html') or filename.endswith('.htm'):
            await _process_html_complaint(file_content, complaint_data)
        elif filename.endswith('.pdf'):
            await _process_pdf_complaint(file_content, complaint_data)

        # Store the file content
        file_id = await _store_file_content(file_content, file.filename or "unknown", file.content_type or "application/octet-stream", db)
        complaint_data["file_id"] = file_id

        return complaint_data

    except Exception as e:
        logger.error(f"Error processing complaint file: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Failed to process complaint file")

async def _process_html_complaint(content: bytes, complaint_data: Dict[str, Any]) -> None:
    """
    Process HTML complaint file and extract relevant information

    Args:
        content: HTML content as bytes
        complaint_data: Dictionary to update with extracted data
    """
    try:
        # Parse HTML content
        soup = BeautifulSoup(content, 'lxml')

        # Extract text content
        text_content = soup.get_text(separator=' ', strip=True)
        complaint_data["extracted_text"] = text_content[:10000]  # Limit text size

        # Try to extract title if not provided
        if complaint_data["title"] == f"Complaint {complaint_data['complaint_number']}":
            title_tag = soup.find('title')
            if title_tag and title_tag.text.strip():
                complaint_data["title"] = title_tag.text.strip()[:100]
            elif soup.h1 and soup.h1.text.strip():
                complaint_data["title"] = soup.h1.text.strip()[:100]

        # Try to extract complainant name
        # This is a simplified example - in production you'd use more sophisticated extraction
        name_patterns = [
            r'complainant\s*name\s*:\s*([A-Za-z\s\.]+)',
            r'name\s*of\s*complainant\s*:\s*([A-Za-z\s\.]+)',
            r'plaintiff\s*:\s*([A-Za-z\s\.]+)'
        ]

        for pattern in name_patterns:
            match = re.search(pattern, text_content, re.IGNORECASE)
            if match:
                complaint_data["complainant_name"] = match.group(1).strip()[:100]
                break

        # Extract complaint date - ONLY from ContentPlaceHolder1_lblComplaintDate
        complaint_date_element = soup.select_one('#ContentPlaceHolder1_lblComplaintDate')
        complaint_date = None

        if complaint_date_element and complaint_date_element.text.strip():
            complaint_date = complaint_date_element.text.strip()
            logger.info(f"Found complaint date in HTML element ContentPlaceHolder1_lblComplaintDate: {complaint_date}")

            # Store the original date string exactly as extracted - this is the FIXED date that should never change
            complaint_data["original_complaint_date"] = complaint_date
            logger.info(f"Stored fixed original complaint date that will never be updated: {complaint_date}")

            # Also store as string in the date field for compatibility
            complaint_data["date"] = complaint_date
            logger.info(f"Using original complaint date string in date field: {complaint_date}")
        else:
            # If no date found in the specific element, use empty string
            complaint_data["date"] = ""
            complaint_data["original_complaint_date"] = ""
            logger.warning("No complaint date found in ContentPlaceHolder1_lblComplaintDate element")

        # Extract metadata
        metadata = {}
        metadata["links"] = len(soup.find_all('a'))
        metadata["images"] = len(soup.find_all('img'))
        metadata["tables"] = len(soup.find_all('table'))
        metadata["paragraphs"] = len(soup.find_all('p'))

        # Add complaint date to metadata
        if complaint_date:
            metadata["date"] = complaint_date

        complaint_data["metadata"] = metadata

    except Exception as e:
        logger.warning(f"Error extracting data from HTML: {str(e)}")
        # Continue with default values if extraction fails
        # Use empty string if date is still None - do NOT use current date
        if complaint_data["date"] is None:
            complaint_data["date"] = ""
            logger.warning("Setting empty string for date due to extraction error - will NOT use current date")

async def _process_pdf_complaint(content: bytes, complaint_data: Dict[str, Any]) -> None:
    """
    Process PDF complaint file and extract relevant information

    Args:
        content: PDF content as bytes
        complaint_data: Dictionary to update with extracted data
    """
    try:
        # In production, you would use a PDF parsing library like PyPDF2, pdfplumber, or pymupdf
        # For this example, we'll simulate extraction

        # Save PDF to temporary file for processing
        with NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            temp_file.write(content)
            temp_path = temp_file.name

        try:
            # Here you would use a PDF library to extract text
            # For example with PyPDF2:
            # from PyPDF2 import PdfReader
            # reader = PdfReader(temp_path)
            # text = ""
            # for page in reader.pages:
            #     text += page.extract_text() + "\n"

            # Simulated extraction for this example
            text = f"Simulated PDF text extraction for {complaint_data['complaint_number']}"
            complaint_data["extracted_text"] = text

            # In production, you would extract more data here

        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    except Exception as e:
        logger.warning(f"Error extracting data from PDF: {str(e)}")
        # Continue with default values if extraction fails

async def _store_file_content(
    content: bytes,
    filename: str,
    content_type: str,
    db: AsyncIOMotorClient
) -> str:
    """
    Store file content in the database or file system

    Args:
        content: File content as bytes
        filename: Original filename
        content_type: MIME type of the file
        db: Database connection

    Returns:
        ID of the stored file
    """
    try:
        # In production, you might want to store large files in a dedicated storage service
        # like S3, Azure Blob Storage, etc. instead of the database

        file_doc = {
            "filename": filename,
            "content_type": content_type,
            "content": content,
            "upload_date": datetime.datetime.now(),
            "size": len(content)
        }

        result = await db.files.insert_one(file_doc)
        return str(result.inserted_id)

    except Exception as e:
        logger.error(f"Error storing file content: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to store file content")

# ✅ Store a new complaint (linked to user)
async def add_complaint(user_id: str, complaint: Complaint, db: AsyncIOMotorClient):
    """
    Add a new complaint to the database.
    This function is async to properly handle MongoDB async operations.

    Args:
        user_id: ID of the user adding the complaint
        complaint: Complaint object to add
        db: Database connection

    Returns:
        ID of the newly added complaint
    """
    complaint_dict = complaint.model_dump()
    complaint_dict["user_id"] = user_id  # Store user reference
    complaint_number = complaint_dict.get("complaint_number")

    # Encrypt sensitive fields before storing
    complaint_dict = encrypt_complaint(complaint_dict)

    try:
        # Check if a complaint with the same complaint_number already exists
        existing_complaint = await db.complaints.find_one({"complaint_number": complaint_number})

        if existing_complaint:
            # Delete the existing complaint first
            complaint_id = str(existing_complaint["_id"])
            logger.info(f"Deleting existing complaint with number {complaint_number} before adding new version")

            delete_result = await db.complaints.delete_one({"_id": ObjectId(complaint_id)})

            if delete_result.deleted_count == 0:
                logger.warning(f"Failed to delete existing complaint with number {complaint_number}")
                # Continue anyway to try to insert the new version
        else:
            # Check user's complaint count limit (50 complaints per user)
            user_complaint_count = await db.complaints.count_documents({"user_id": user_id})

            if user_complaint_count >= 50:
                logger.warning(f"User {user_id} has reached the 50 complaint limit. Removing oldest complaint.")

                # Find the oldest complaint by this user
                oldest_complaint = await db.complaints.find_one(
                    {"user_id": user_id},
                    sort=[("upload_date", 1)]  # Sort by upload date ascending (oldest first)
                )

                if oldest_complaint:
                    # Delete the oldest complaint
                    oldest_id = str(oldest_complaint["_id"])
                    logger.info(f"Deleting oldest complaint {oldest_id} to make room for new complaint")

                    delete_result = await db.complaints.delete_one({"_id": ObjectId(oldest_id)})

                    if delete_result.deleted_count == 0:
                        logger.warning(f"Failed to delete oldest complaint {oldest_id}")
                        raise HTTPException(status_code=500, detail="Failed to manage complaint limit")

        # Insert the new complaint
        result = await db.complaints.insert_one(complaint_dict)
        if result.inserted_id:
            logger.info(f"Complaint added for user {user_id}, complaint_number: {complaint_number}")

            # Update user's complaint count
            await db.users.update_one(
                {"_id": ObjectId(user_id)},
                {"$inc": {"complaint_count": 1}}
            )

            return str(result.inserted_id)
        else:
            logger.error(f"Failed to insert complaint for user {user_id}, complaint_number: {complaint_number}")
            raise HTTPException(status_code=500, detail="Failed to store complaint")

    except Exception as e:
        logger.error(f"Failed to add/update complaint: {e}")
        raise HTTPException(status_code=500, detail="Database error")

# ✅ Fetch complaints for a specific user
async def get_complaints(user_id: str, db: AsyncIOMotorClient):
    try:
        # Log the user_id for debugging
        logger.info(f"Fetching complaints for user_id: {user_id}")

        # Try to find complaints with the exact user_id string
        complaints = await db.complaints.find({"user_id": user_id}).to_list(None)

        # If no complaints found, log this information
        if not complaints:
            logger.info(f"No complaints found for user_id: {user_id}")
        else:
            logger.info(f"Found {len(complaints)} complaints for user_id: {user_id}")

        # Process each complaint
        processed_complaints = []
        for complaint in complaints:
            # Convert ObjectId to string
            complaint["_id"] = str(complaint["_id"])

            # Handle bank_notice_data if it's a string
            if 'bank_notice_data' in complaint and isinstance(complaint['bank_notice_data'], str):
                try:
                    import json
                    complaint['bank_notice_data'] = json.loads(complaint['bank_notice_data'])
                except Exception as e:
                    logger.error(f"Failed to convert bank_notice_data string to dictionary for complaint {complaint.get('complaint_number', 'unknown')}: {e}")

            # Decrypt sensitive fields
            processed_complaints.append(decrypt_complaint(complaint))

        return processed_complaints
    except Exception as e:
        logger.error(f"Error fetching complaints for user {user_id}: {e}")
        raise HTTPException(status_code=500, detail="Database error")

# ✅ Get a specific complaint (to verify ownership before update/delete)
async def get_complaint_by_id(complaint_id: str, db: AsyncIOMotorClient):
    try:
        # Try to find by ID first
        query = {}
        try:
            # Check if complaint_id is a valid ObjectId
            query = {"_id": ObjectId(complaint_id)}
        except:
            # If not, try to find by complaint_number
            query = {"complaint_number": complaint_id}

        logger.info(f"Looking for complaint with query: {query}")
        complaint = await db.complaints.find_one(query)

        if not complaint:
            logger.warning(f"Complaint not found with ID or number: {complaint_id}")
            raise HTTPException(status_code=404, detail="Complaint not found")

        complaint["_id"] = str(complaint["_id"])  # Convert ObjectId to string

        # Handle bank_notice_data if it's a string
        if 'bank_notice_data' in complaint and isinstance(complaint['bank_notice_data'], str):
            try:
                import json
                complaint['bank_notice_data'] = json.loads(complaint['bank_notice_data'])
                logger.info(f"Converted bank_notice_data string to dictionary for complaint {complaint_id}")
            except Exception as e:
                logger.error(f"Failed to convert bank_notice_data string to dictionary for complaint {complaint_id}: {e}")
                # Set to empty dict if conversion fails
                complaint['bank_notice_data'] = {}

        # Handle graph_data if it's a string
        if 'graph_data' in complaint and isinstance(complaint['graph_data'], str):
            try:
                import json
                complaint['graph_data'] = json.loads(complaint['graph_data'])
                logger.info(f"Converted graph_data string to dictionary for complaint {complaint_id}")
            except Exception as e:
                logger.error(f"Failed to convert graph_data string to dictionary for complaint {complaint_id}: {e}")
                # Set to empty dict if conversion fails
                complaint['graph_data'] = {}

        # Decrypt sensitive fields
        decrypted_complaint = decrypt_complaint(complaint)

        # Log if summary exists in the complaint
        if "summary" in complaint:
            logger.info(f"Found summary in complaint {complaint_id}")
            if isinstance(complaint["summary"], dict):
                logger.info(f"Summary is a dictionary with keys: {complaint['summary'].keys()}")
            else:
                logger.info(f"Summary is of type: {type(complaint['summary'])}")
        else:
            logger.info(f"No summary found in complaint {complaint_id}")

        # Process metadata for consistency with get_complaint_graph_data
        # Extract metadata from graph_data if available
        graph_data = decrypted_complaint.get("graph_data", {})
        graph_metadata = None

        if isinstance(graph_data, dict) and "metadata" in graph_data:
            graph_metadata = graph_data.get("metadata")
            logger.info(f"Found metadata in graph_data for complaint {complaint_id}")

            # Process complainant_name if needed
            if graph_metadata and isinstance(graph_metadata, dict):
                if "complainant_name" in graph_metadata and graph_metadata["complainant_name"]:
                    try:
                        from services.notice_service import process_complainant_name
                        graph_metadata["complainant_name"] = process_complainant_name(graph_metadata)
                        logger.info(f"Processed complainant name in graph metadata")
                    except Exception as e:
                        logger.error(f"Error processing complainant name in graph metadata: {e}")

        # Start with complaint's metadata field as the primary source
        complaint_metadata = decrypted_complaint.get("metadata", {})

        # Handle metadata that might be stored as JSON string (for backward compatibility)
        if isinstance(complaint_metadata, str):
            try:
                import json
                complaint_metadata = json.loads(complaint_metadata)
                logger.info("Successfully parsed metadata from JSON string")
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse metadata JSON string: {e}, using empty dict")
                complaint_metadata = {}
        elif not isinstance(complaint_metadata, dict):
            logger.warning(f"complaint_metadata is not a dict or string, type: {type(complaint_metadata)}, converting to empty dict")
            complaint_metadata = {}

        # Create comprehensive metadata by merging complaint metadata with basic fields
        metadata = {
            # Basic complaint fields
            "complaint_number": decrypted_complaint.get("complaint_number"),
            "complainant_name": decrypted_complaint.get("complainant_name"),
            "amount": decrypted_complaint.get("amount"),
            "category": decrypted_complaint.get("category"),
            "subcategory": decrypted_complaint.get("subcategory"),
            "date": decrypted_complaint.get("date"),
            "status": decrypted_complaint.get("status")
        }

        # Add all fields from complaint metadata if it's a valid dict
        if complaint_metadata:
            metadata.update(complaint_metadata)

        # If graph_data has metadata, merge it as well (but complaint metadata takes precedence)
        if graph_metadata:
            # Only add graph metadata fields that don't exist in complaint metadata
            for key, value in graph_metadata.items():
                if key not in metadata:
                    metadata[key] = value

        # Make sure we don't have "CSV Import" as complainant_name if we have a real name
        if metadata.get("complainant_name") == "CSV Import" and decrypted_complaint.get("complainant_name") and decrypted_complaint.get("complainant_name") != "CSV Import":
            metadata["complainant_name"] = decrypted_complaint.get("complainant_name")

        # Ensure total_amount is set
        if not metadata.get("total_amount") and decrypted_complaint.get("amount"):
            metadata["total_amount"] = decrypted_complaint.get("amount")

        # Update the graph_data metadata with our corrected metadata
        if isinstance(graph_data, dict):
            graph_data["metadata"] = metadata
            decrypted_complaint["graph_data"] = graph_data

        # Add metadata directly to the complaint for consistency
        decrypted_complaint["metadata"] = metadata

        return decrypted_complaint
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error fetching complaint {complaint_id}: {e}")
        raise HTTPException(status_code=500, detail="Database error")

# ✅ Update complaint (Only if it belongs to the user)
async def update_complaint(complaint_id: str, user_id: str, update_data: dict, db: AsyncIOMotorClient):
    try:
        # Log the update data keys and check for bank_notice_data
        logger.info(f"Updating complaint {complaint_id} with fields: {', '.join(update_data.keys())}")

        # Ensure bank_notice_data is properly handled
        if 'bank_notice_data' in update_data:
            if update_data['bank_notice_data'] is None:
                logger.warning(f"bank_notice_data is None for complaint {complaint_id} in update")
            elif isinstance(update_data['bank_notice_data'], dict):
                logger.info(f"bank_notice_data is a dictionary with {len(update_data['bank_notice_data'])} keys")
                # Log some details about the bank_notice_data
                for bank_name, bank_data in update_data['bank_notice_data'].items():
                    if bank_name != '_metadata':
                        logger.info(f"Bank: {bank_name} with {len(bank_data)} accounts")
            elif isinstance(update_data['bank_notice_data'], str):
                logger.info(f"bank_notice_data is a string of length {len(update_data['bank_notice_data'])}")
                # Try to convert string to dict if it's a JSON string
                try:
                    import json
                    update_data['bank_notice_data'] = json.loads(update_data['bank_notice_data'])
                    logger.info(f"Successfully converted bank_notice_data string to dictionary with {len(update_data['bank_notice_data'])} keys")
                except Exception as e:
                    logger.error(f"Failed to convert bank_notice_data string to dictionary: {e}")
            else:
                logger.warning(f"bank_notice_data is of unexpected type: {type(update_data['bank_notice_data'])}")

        # Ensure summary is properly handled
        if 'summary' in update_data:
            if update_data['summary'] is None:
                logger.warning(f"summary is None for complaint {complaint_id} in update")
            elif isinstance(update_data['summary'], dict):
                logger.info(f"summary is a dictionary with {len(update_data['summary'])} keys: {update_data['summary'].keys()}")
                # Log some key details from the summary
                if 'complainant_details' in update_data['summary']:
                    logger.info(f"Complainant name: {update_data['summary']['complainant_details'].get('name', 'Not found')}")
                    logger.info(f"Complainant mobile: {update_data['summary']['complainant_details'].get('mobile', 'Not found')}")
                if 'financial_details' in update_data['summary']:
                    logger.info(f"Total fraudulent amount: {update_data['summary']['financial_details'].get('total_fraudulent_amount', 'Not found')}")
            elif isinstance(update_data['summary'], str):
                logger.info(f"summary is a string of length {len(update_data['summary'])}")
                # Try to convert string to dict if it's a JSON string
                try:
                    import json
                    update_data['summary'] = json.loads(update_data['summary'])
                    logger.info(f"Successfully converted summary string to dictionary with {len(update_data['summary'])} keys: {update_data['summary'].keys()}")
                except Exception as e:
                    logger.error(f"Failed to convert summary string to dictionary: {e}")
            else:
                logger.warning(f"summary is of unexpected type: {type(update_data['summary'])}")

        # Try to find the complaint by ID or complaint number
        query = {}
        try:
            # Check if complaint_id is a valid ObjectId
            query = {"_id": ObjectId(complaint_id), "user_id": user_id}
        except:
            # If not, try to find by complaint_number
            query = {"complaint_number": complaint_id, "user_id": user_id}

        logger.info(f"Updating complaint with query: {query}")

        # Log the update operation for debugging
        logger.info(f"Updating complaint {complaint_id} with query: {query}")
        logger.info(f"Update data keys: {update_data.keys()}")

        # Update the complaint
        result = await db.complaints.update_one(query, {"$set": update_data})

        if result.modified_count == 0:
            logger.warning(f"No changes made to complaint {complaint_id}")
            # Check if the complaint exists but no changes were made
            complaint = await db.complaints.find_one(query)
            if complaint:
                logger.info(f"Complaint {complaint_id} exists but no changes were made")
                # Check if summary already exists and matches
                if 'summary' in update_data and 'summary' in complaint:
                    logger.info(f"Complaint already has summary with keys: {complaint['summary'].keys() if isinstance(complaint['summary'], dict) else 'Not a dict'}")
                return True  # Return true if the complaint exists but no changes were made
            return False

        # Verify the update was successful by retrieving the updated document
        updated_complaint = await db.complaints.find_one(query)
        if updated_complaint and 'summary' in update_data and 'summary' in updated_complaint:
            logger.info(f"Verified summary was stored with keys: {updated_complaint['summary'].keys() if isinstance(updated_complaint['summary'], dict) else 'Not a dict'}")

        logger.info(f"Successfully updated complaint {complaint_id}")
        return True
    except Exception as e:
        logger.error(f"Error updating complaint {complaint_id}: {e}")
        raise HTTPException(status_code=500, detail="Database error")

# ✅ Delete complaint (Only if it belongs to the user)
async def delete_complaint(complaint_id: str, user_id: str, db: AsyncIOMotorClient):
    try:
        # Try to find the complaint by ID or complaint number
        query = {}
        try:
            # Check if complaint_id is a valid ObjectId
            query = {"_id": ObjectId(complaint_id), "user_id": user_id}
        except:
            # If not, try to find by complaint_number
            query = {"complaint_number": complaint_id, "user_id": user_id}

        logger.info(f"Deleting complaint with query: {query}")

        # Delete the complaint
        result = await db.complaints.delete_one(query)

        if result.deleted_count == 0:
            logger.warning(f"No complaint deleted with ID or number: {complaint_id}")
            raise HTTPException(status_code=404, detail="Complaint not found or unauthorized to delete")

        # Update user's complaint count (decrement by 1)
        await db.users.update_one(
            {"_id": ObjectId(user_id)},
            {"$inc": {"complaint_count": -1}}
        )

        logger.info(f"Successfully deleted complaint {complaint_id} and updated user complaint count")
        return {"message": "Complaint deleted successfully"}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error deleting complaint {complaint_id}: {e}")
        raise HTTPException(status_code=500, detail="Database error")

# ✅ Get graph visualization data for a specific complaint
async def get_complaint_graph_data(complaint_id: str, user_id: str, db: AsyncIOMotorClient):
    """Get graph visualization data for a specific complaint.
    This function is now asynchronous to properly await database operations.

    Args:
        complaint_id: ID of the complaint or complaint number
        user_id: ID of the user requesting the data (for authorization)
        db: Database connection

    Returns:
        Dictionary containing graph data, notice data, and metadata
    """
    try:
        # Try to find by ID first
        query = {}
        try:
            # Check if complaint_id is a valid ObjectId
            query = {"_id": ObjectId(complaint_id), "user_id": user_id}
        except:
            # If not, try to find by complaint_number
            query = {"complaint_number": complaint_id, "user_id": user_id}

        complaint = await db.complaints.find_one(query)

        if not complaint:
            raise HTTPException(status_code=404, detail="Complaint not found or unauthorized access")

        # Convert ObjectId to string
        complaint["_id"] = str(complaint["_id"])

        # Handle bank_notice_data if it's a string
        bank_notice_data = complaint.get("bank_notice_data")
        if isinstance(bank_notice_data, str):
            try:
                import json
                bank_notice_data = json.loads(bank_notice_data)
                logger.info(f"Converted bank_notice_data string to dictionary for complaint {complaint_id} in graph data")
            except Exception as e:
                logger.error(f"Failed to convert bank_notice_data string to dictionary for complaint {complaint_id} in graph data: {e}")
                bank_notice_data = {}

        # Extract only the necessary data for visualization

        # First check if we have metadata in graph_data
        graph_data = complaint.get("graph_data", {})
        graph_metadata = None

        # First, decrypt the complaint to get decrypted fields
        decrypted_complaint = decrypt_complaint(complaint)
        logger.info(f"Decrypted complaint data for metadata extraction")

        if isinstance(graph_data, dict) and "metadata" in graph_data:
            graph_metadata = graph_data.get("metadata")
            logger.info(f"Found metadata in graph_data")

            # Decrypt the metadata if it contains encrypted fields
            if graph_metadata and isinstance(graph_metadata, dict):
                # Check if complainant_name is encrypted
                if "complainant_name" in graph_metadata and graph_metadata["complainant_name"]:
                    try:
                        from services.notice_service import process_complainant_name
                        graph_metadata["complainant_name"] = process_complainant_name(graph_metadata)
                        logger.info(f"Processed complainant name in graph metadata")
                    except Exception as e:
                        logger.error(f"Error processing complainant name in graph metadata: {e}")

        # Start with complaint's metadata field as the primary source
        complaint_metadata = decrypted_complaint.get("metadata", {})

        # Handle metadata that might be stored as JSON string (for backward compatibility)
        if isinstance(complaint_metadata, str):
            try:
                import json
                complaint_metadata = json.loads(complaint_metadata)
                logger.info("Successfully parsed metadata from JSON string")
            except (json.JSONDecodeError, TypeError) as e:
                logger.warning(f"Failed to parse metadata JSON string: {e}, using empty dict")
                complaint_metadata = {}
        elif not isinstance(complaint_metadata, dict):
            logger.warning(f"complaint_metadata is not a dict or string, type: {type(complaint_metadata)}, converting to empty dict")
            complaint_metadata = {}

        # Create comprehensive metadata by merging complaint metadata with basic fields
        metadata = {
            # Basic complaint fields
            "complaint_number": decrypted_complaint.get("complaint_number"),
            "complainant_name": decrypted_complaint.get("complainant_name"),
            "amount": decrypted_complaint.get("amount"),
            "category": decrypted_complaint.get("category"),
            "subcategory": decrypted_complaint.get("subcategory"),
            "date": decrypted_complaint.get("date"),
            "status": decrypted_complaint.get("status")
        }

        # Add all fields from complaint metadata if it's a valid dict
        if complaint_metadata:
            metadata.update(complaint_metadata)

        # If graph_data has metadata, merge it as well (but complaint metadata takes precedence)
        if graph_metadata:
            # Only add graph metadata fields that don't exist in complaint metadata
            for key, value in graph_metadata.items():
                if key not in metadata:
                    metadata[key] = value

        # Make sure we don't have "CSV Import" as complainant_name if we have a real name in the complaint
        if metadata.get("complainant_name") == "CSV Import" and decrypted_complaint.get("complainant_name") and decrypted_complaint.get("complainant_name") != "CSV Import":
            metadata["complainant_name"] = decrypted_complaint.get("complainant_name")
            logger.info(f"Replaced 'CSV Import' with actual complainant name")

            # If we have graph_data, update its metadata too
            if isinstance(graph_data, dict) and "metadata" in graph_data:
                graph_data["metadata"] = metadata
                logger.info("Updated graph_data metadata with actual complainant name")

        # Make sure all other metadata fields are properly set
        if metadata.get("complaint_number") == "CSV-IMPORT" and decrypted_complaint.get("complaint_number"):
            metadata["complaint_number"] = decrypted_complaint.get("complaint_number")
            logger.info(f"Updated complaint_number in metadata")

        # Always use the original complaint date from the original_complaint_date field
        if decrypted_complaint.get("original_complaint_date"):
            metadata["date"] = decrypted_complaint.get("original_complaint_date")
            logger.info(f"Using original extracted complaint date in metadata: {metadata['date']}")
        # Fallback to date field if original_complaint_date is not available
        elif decrypted_complaint.get("date"):
            complaint_date = decrypted_complaint.get("date")
            if isinstance(complaint_date, datetime.datetime):
                formatted_date = complaint_date.strftime('%d/%m/%Y')
                metadata["date"] = formatted_date
            else:
                metadata["date"] = str(complaint_date)
            logger.info(f"Using date field as fallback in metadata: {metadata['date']}")

        if not metadata.get("total_amount") and decrypted_complaint.get("amount"):
            metadata["total_amount"] = decrypted_complaint.get("amount")
            logger.info(f"Updated total_amount in metadata")

        # Update the graph_data metadata with our corrected metadata
        if isinstance(graph_data, dict):
            graph_data["metadata"] = metadata

        result = {
            "metadata": metadata,
            "graph_data": graph_data,
            "bank_notice_data": bank_notice_data,
            "notice_docx_base64": complaint.get("notice_docx_base64"),
            "csv_data_base64": complaint.get("csv_data_base64")
        }

        return result
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error fetching graph data for complaint {complaint_id}: {e}")
        raise HTTPException(status_code=500, detail="Database error")

# This function is now synchronous since indexes are created in database.py
def create_indexes(_db):
    # No need to create indexes here as they're created in database.py
    return True
