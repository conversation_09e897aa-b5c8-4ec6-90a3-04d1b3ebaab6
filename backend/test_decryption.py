#!/usr/bin/env python3
"""
Test script to check if complaint decryption is working properly
"""

import asyncio
import logging
import sys
import os

# Add the parent directory to the path to import from backend
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import get_db
from services.complaint_service import get_complaint_by_id
from services.encryption_service import decrypt_complaint

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_complaint_decryption():
    """
    Test complaint decryption by fetching a few complaints and checking if names are properly decrypted
    """
    try:
        # Get database connection
        db = get_db()
        if not db:
            logger.error("Failed to connect to database")
            return False

        # Find a few complaints to test
        complaints = await db.complaints.find({}).limit(3).to_list(None)

        if not complaints:
            logger.info("No complaints found in database")
            return True

        logger.info(f"Found {len(complaints)} complaints to test")

        for complaint in complaints:
            complaint_id = str(complaint.get("_id"))
            logger.info(f"\n--- Testing complaint {complaint_id} ---")

            # Test raw complaint data
            raw_complainant_name = complaint.get("complainant_name", "Not found")
            logger.info(f"Raw complainant_name: {raw_complainant_name}")
            logger.info(f"Raw complainant_name type: {type(raw_complainant_name)}")

            # Test direct decryption
            try:
                decrypted_complaint = decrypt_complaint(complaint)
                decrypted_name = decrypted_complaint.get("complainant_name", "Not found")
                logger.info(f"Directly decrypted complainant_name: {decrypted_name}")
                logger.info(f"Directly decrypted complainant_name type: {type(decrypted_name)}")
            except Exception as e:
                logger.error(f"Error in direct decryption: {e}")

            # Test get_complaint_by_id function
            try:
                processed_complaint = await get_complaint_by_id(complaint_id, db)
                processed_name = processed_complaint.get("complainant_name", "Not found")
                logger.info(f"get_complaint_by_id complainant_name: {processed_name}")
                logger.info(f"get_complaint_by_id complainant_name type: {type(processed_name)}")

                # Check metadata
                metadata = processed_complaint.get("metadata", {})
                if isinstance(metadata, dict):
                    metadata_name = metadata.get("complainant_name", "Not found in metadata")
                    logger.info(f"Metadata complainant_name: {metadata_name}")
                else:
                    logger.info(f"Metadata is not a dict: {type(metadata)}")

            except Exception as e:
                logger.error(f"Error in get_complaint_by_id: {e}")

        return True

    except Exception as e:
        logger.error(f"Test failed: {str(e)}")
        return False

async def main():
    """
    Main function to run the test
    """
    logger.info("Starting complaint decryption test...")

    success = await test_complaint_decryption()
    if success:
        logger.info("Test completed successfully")
    else:
        logger.error("Test failed")

if __name__ == "__main__":
    asyncio.run(main())
