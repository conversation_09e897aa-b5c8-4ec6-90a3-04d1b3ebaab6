{"name": "cyber-sakha-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:prod": "vite build --mode production", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "tailwindinit": "tailwindcss init --ts -p", "analyze": "vite build --mode analyze", "test": "echo \"No tests configured\"", "clean": "rm -rf dist node_modules/.vite", "start": "vite preview --host 0.0.0.0 --port 3001", "deploy": "npm run build:prod && npm run start"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tanstack/react-table": "^8.21.3", "@types/dagre": "^0.7.52", "@types/node": "^20.11.0", "axios": "^1.8.4", "dagre": "^0.8.5", "framer-motion": "^12.7.4", "html-to-image": "^1.11.13", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.2.3", "react-icons": "^5.5.0", "react-router-dom": "^7.4.1", "react-to-print": "^3.1.0", "react-toastify": "^11.0.5", "reactflow": "^11.11.4", "@tanstack/react-query": "^5.51.11", "@tanstack/react-query-devtools": "^5.51.11", "tailwind-merge": "^3.2.0", "typescript": "^5.3.3"}, "devDependencies": {"@eslint/js": "^9.21.0", "@types/html2canvas": "^0.5.35", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.16", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^15.15.0", "postcss": "^8.4.31", "tailwindcss": "^3.4.0", "terser": "^5.39.2", "vite": "^6.2.0"}}