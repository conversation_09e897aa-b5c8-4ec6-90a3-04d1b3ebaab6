import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { TailwindThemeProvider } from './context/TailwindThemeContext';
import { TailwindAlertProvider } from './context/TailwindAlertContext';
import { AuthProvider } from './context/AuthContext';
import { LayoutProvider } from './context/LayoutContext';
import ProtectedRoute from './components/ProtectedRoute';
import { useAuth } from './context/AuthContext';
import ErrorBoundary from './components/common/ErrorBoundary';
import ErrorFallback from './components/common/ErrorFallback';
import logger from './utils/logger';

// Component to handle default route redirection based on authentication status
const DefaultRedirect: React.FC = () => {
  const { loading, isAuthenticated, refreshUserData } = useAuth();

  // Effect to refresh user data when component mounts
  React.useEffect(() => {
    // Refresh user data regardless of token in sessionStorage
    // The token is now in HttpOnly cookies
    logger.debug('Default redirect: Refreshing user data');
    refreshUserData().catch(err => {
      logger.error('Error refreshing user data in DefaultRedirect:', err);
    });
  }, [refreshUserData]);

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Check authentication status
  logger.debug('Default redirect: Authentication status =', isAuthenticated);

  if (isAuthenticated) {
    logger.debug('Default redirect: User is authenticated, redirecting to dashboard');
    return <Navigate to="/dashboard" replace />;
  } else {
    logger.debug('Default redirect: User is not authenticated, redirecting to login');
    return <Navigate to="/login" replace />;
  }
};

// Layouts
import TailwindLayout from './components/layout/TailwindLayout';

// Loading component for Suspense fallback
const LoadingComponent = () => (
  <div className="flex items-center justify-center min-h-screen">
    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
  </div>
);

// Lazy load pages for better performance
// Auth related pages (load immediately as they're critical)
import TailwindAuthForm from './components/TailwindAuthForm';
import TailwindPasswordReset from './pages/TailwindPasswordReset';
import VerifyEmail from './pages/VerifyEmail';
import ResendVerification from './pages/ResendVerification';
import TailwindNotFound from './pages/TailwindNotFound';

// Lazy loaded pages (load on demand)
const TailwindDashboard = lazy(() => import('./pages/TailwindDashboard'));
const TailwindSimpleFileUpload = lazy(() => import('./pages/TailwindSimpleFileUpload'));
const ProfilePage = lazy(() => import('./pages/ProfilePage'));
const SettingsPage = lazy(() => import('./pages/SettingsPage'));
const GraphVisualization = lazy(() => import('./pages/GraphVisualization'));
const NoticeGenerationPage = lazy(() => import('./pages/NoticeGenerationPage'));
const ComplaintDetail = lazy(() => import('./pages/ComplaintDetailNew'));
const ComplaintSummary = lazy(() => import('./pages/ComplaintSummary'));
const InformationSearchPage = lazy(() => import('./pages/InformationSearchPage'));
const DataUpload = lazy(() => import('./pages/DataUpload'));
const ComplaintAnalysisPage = lazy(() => import('./pages/ComplaintAnalysisPage'));

// Custom error handler for the global error boundary
const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
  logger.error('Global error caught:', error);
  logger.error('Component stack:', errorInfo.componentStack);

  // You could send this to a monitoring service like Sentry here
};

const App: React.FC = () => {
  return (
    <ErrorBoundary
      fallback={<ErrorFallback showDetails={import.meta.env.MODE !== 'production'} />}
      onError={handleError}
    >
      <TailwindThemeProvider>
        <TailwindAlertProvider>
          <AuthProvider>
              <Router>
                <LayoutProvider>
                  <Routes>
                  {/* Public routes */}
                  <Route path="/login" element={<TailwindAuthForm />} />
                  <Route path="/register" element={<TailwindAuthForm isRegister={true} />} />

                  {/* Public routes - Auth related */}
                  <Route path="/forgot-password" element={<TailwindPasswordReset />} />
                  <Route path="/reset-password/:token" element={<TailwindPasswordReset />} />
                  <Route path="/verify-email/:token" element={<VerifyEmail />} />
                  <Route path="/resend-verification" element={<ResendVerification />} />

                  {/* Protected routes - Each wrapped in its own ErrorBoundary */}
                  <Route path="/dashboard" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <TailwindDashboard />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/complaint-upload" element={
                    <ProtectedRoute requirePaid={true}>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <TailwindSimpleFileUpload />
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/profile" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <ProfilePage />
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/settings" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <SettingsPage />
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  {/* Support both singular and plural routes for backward compatibility */}
                  <Route path="/complaint/:id" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <ComplaintDetail />
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />
                  <Route path="/complaints/:id" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <ComplaintDetail />
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/graph/:id" element={
                    <ProtectedRoute requirePaid={true}>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <GraphVisualization />
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/complaint-summary/:id" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <ComplaintSummary />
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/notices/generate/:id" element={
                    <ProtectedRoute requirePaid={true}>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <NoticeGenerationPage />
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/information-search" element={
                    <ProtectedRoute requirePaid={true}>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <InformationSearchPage />
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/data-upload" element={
                    <ProtectedRoute>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <DataUpload />
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  <Route path="/complaint-analysis" element={
                    <ProtectedRoute requirePaid={true}>
                      <ErrorBoundary fallback={<ErrorFallback />}>
                        <TailwindLayout>
                          <Suspense fallback={<LoadingComponent />}>
                            <ComplaintAnalysisPage />
                          </Suspense>
                        </TailwindLayout>
                      </ErrorBoundary>
                    </ProtectedRoute>
                  } />

                  {/* Default route - redirect based on authentication status */}
                  <Route path="/" element={<DefaultRedirect />} />

                  {/* Catch-all route */}
                  <Route path="*" element={<TailwindNotFound />} />
                  </Routes>
                </LayoutProvider>
              </Router>
          </AuthProvider>
        </TailwindAlertProvider>
      </TailwindThemeProvider>
    </ErrorBoundary>
  );
};

export default App;
