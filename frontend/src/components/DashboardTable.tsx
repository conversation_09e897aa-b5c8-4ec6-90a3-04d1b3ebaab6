import React, { useMemo } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  flexRender,
  createColumnHelper,
  SortingState,
  getPaginationRowModel,
  PaginationState,
} from '@tanstack/react-table';
import { FiEye, FiEdit, FiTrash2, FiAlertCircle, FiCheckCircle, FiClock, FiChevronLeft, FiChevronRight, FiFileText } from 'react-icons/fi';
import Badge from './ui/Badge';
import Button from './ui/Button';
import Pagination from './ui/Pagination';

// Types
interface Complaint {
  _id?: string;
  complaint_number: string;
  complainant_name: string;
  category: string;
  amount: string | number;
  status: string;
  metadata?: {
    total_amount?: string | number;
    [key: string]: any;
  };
}

interface DashboardTableProps {
  complaints: Complaint[];
  loading: boolean;
  onView: (complaintNumber: string) => void;
  onEdit: (complaint: Complaint) => void;
  onDelete: (complaintNumber: string) => void;
  onSummary: (complaintNumber: string) => void;
  pagination?: {
    page: number;
    pages: number;
    total: number;
    limit: number;
  };
  onPageChange?: (page: number) => void;
}

const DashboardTable: React.FC<DashboardTableProps> = ({
  complaints,
  loading,
  onView,
  onEdit,
  onDelete,
  onSummary,
  pagination,
  onPageChange,
}) => {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [{ pageIndex, pageSize }, setPagination] = React.useState<PaginationState>({
    pageIndex: (pagination?.page || 1) - 1,
    pageSize: pagination?.limit || 10,
  });

  // Update internal pagination state when external pagination changes
  // Use a ref to track if this is the initial render to avoid unnecessary updates
  const isInitialRender = React.useRef(true);

  React.useEffect(() => {
    if (pagination) {
      // Only update if it's not the initial render or if the values actually changed
      if (!isInitialRender.current ||
          pageIndex !== pagination.page - 1 ||
          pageSize !== pagination.limit) {
        setPagination({
          pageIndex: pagination.page - 1,
          pageSize: pagination.limit,
        });
      }

      // Set initial render to false after first run
      isInitialRender.current = false;
    }
  }, [pagination, pageIndex, pageSize]);

  const columnHelper = createColumnHelper<Complaint>();
  const isDark = document.documentElement.classList.contains('dark');

  // Format currency
  const formatCurrency = (amount: number | string | undefined | null) => {
    // Handle undefined or null
    if (amount === undefined || amount === null) {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(0);
    }

    // Convert to number if it's a string
    let numAmount = 0;

    if (typeof amount === 'number') {
      numAmount = amount;
    } else if (typeof amount === 'string') {
      // Remove any non-numeric characters except decimal point
      const amountStr = amount.replace(/[^0-9.]/g, '');
      numAmount = parseFloat(amountStr) || 0;
    }

    // Format with Indian Rupee symbol
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(numAmount);
  };

  // Get status badge variant with cyberpunk styling
  const getStatusBadge = (status: string) => {
    const lowercaseStatus = status?.toLowerCase() || '';

    if (lowercaseStatus === 'resolved') {
      return (
        <Badge variant="success" size="sm" rounded className="dark:shadow-glow-green-sm">
          <div className="flex items-center">
            <FiCheckCircle className="mr-1.5 w-3.5 h-3.5" />
            Resolved
          </div>
        </Badge>
      );
    }

    if (lowercaseStatus === 'pending') {
      return (
        <Badge variant="warning" size="sm" rounded className="dark:shadow-glow-red-sm">
          <div className="flex items-center">
            <FiClock className="mr-1.5 w-3.5 h-3.5" />
            Pending
          </div>
        </Badge>
      );
    }

    if (lowercaseStatus === 'in progress') {
      return (
        <Badge variant="info" size="sm" rounded className="dark:shadow-glow-blue-sm">
          <div className="flex items-center">
            <FiClock className="mr-1.5 w-3.5 h-3.5" />
            In Progress
          </div>
        </Badge>
      );
    }

    return (
      <Badge variant="default" size="sm" rounded className="dark:shadow-glow-blue-sm">
        <div className="flex items-center">
          <FiAlertCircle className="mr-1.5 w-3.5 h-3.5" />
          {status || 'Unknown'}
        </div>
      </Badge>
    );
  };

  // Define columns without the serial number first
  const baseColumns = useMemo(
    () => [
      columnHelper.accessor('complaint_number', {
        header: 'Complaint',
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('complainant_name', {
        header: 'Complainant',
        cell: info => info.getValue(),
      }),
      columnHelper.accessor('category', {
        header: 'Category',
        cell: info => info.getValue(),
      }),
      columnHelper.accessor(row => row.metadata?.total_amount || row.amount || 0, {
        id: 'amount',
        header: 'Amount',
        cell: info => formatCurrency(info.getValue()),
      }),
      columnHelper.accessor('status', {
        header: 'Status',
        cell: info => getStatusBadge(info.getValue()),
      }),
      columnHelper.display({
        id: 'actions',
        header: 'Actions',
        cell: props => (
          <div className="flex flex-row justify-end gap-1">
            <Button
              variant="text"
              size="sm"
              onClick={() => onView(props.row.original.complaint_number)}
              className="p-1 min-w-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
              aria-label="View complaint"
            >
              <FiEye className="w-3.5 h-3.5" />
            </Button>

            <Button
              variant="text"
              size="sm"
              onClick={() => onSummary(props.row.original.complaint_number)}
              className="p-1 min-w-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
              aria-label="View complaint summary"
            >
              <FiFileText className="w-3.5 h-3.5" />
            </Button>

            <Button
              variant="text"
              size="sm"
              onClick={() => onEdit(props.row.original)}
              className="p-1 min-w-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
              aria-label="Edit complaint"
            >
              <FiEdit className="w-3.5 h-3.5" />
            </Button>

            <Button
              variant="text"
              size="sm"
              onClick={() => onDelete(props.row.original.complaint_number)}
              className="p-1 min-w-0 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-full"
              aria-label="Delete complaint"
            >
              <FiTrash2 className="w-3.5 h-3.5" />
            </Button>
          </div>
        ),
      }),
    ],
    []
  );

  // We'll create the table after defining all columns

  // Now define the complete columns array with the serial number
  const columns = useMemo(
    () => {
      const columnsList = [];

      // Serial Number column
      columnsList.push(
        columnHelper.display({
          id: 'serialNumber',
          header: () => <div className="text-center">S.No</div>,
          cell: (info) => {
            // Calculate serial number based on pagination
            const rowIndex = info.row.index;

            // If using server-side pagination
            if (pagination) {
              return (
                <div className="text-center font-medium">
                  {((pagination.page - 1) * pagination.limit) + rowIndex + 1}
                </div>
              );
            }

            // If using client-side pagination
            return (
              <div className="text-center font-medium">
                {(pageIndex * pageSize) + rowIndex + 1}
              </div>
            );
          },
        })
      );

      // Add all the base columns
      columnsList.push(...baseColumns);

      return columnsList;
    },
    [baseColumns, pagination, pageIndex, pageSize]
  );

  // Create the table instance at the top level (not inside useMemo)
  const table = useReactTable({
    data: complaints,
    columns,
    state: {
      sorting,
      pagination: { pageIndex, pageSize },
    },
    onSortingChange: setSorting,
    onPaginationChange: setPagination,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    manualPagination: !!pagination, // Use manual pagination if pagination prop is provided
    pageCount: pagination?.pages || Math.ceil(complaints.length / pageSize),
  });

  // Render the table directly instead of using useMemo
  return (
    <div className="w-full">
      <div className="w-full border border-gray-300 dark:border-[var(--theme-border)] max-w-full excel-grid dark:shadow-glow-blue-sm">
        <table className="w-full excel-table table-fixed" style={{
          width: '100%',
          borderSpacing: '0',
          borderCollapse: 'collapse',
          border: 'none',
          tableLayout: 'fixed'
        }}>
          <thead className="sticky top-0 z-10">
            {table.getHeaderGroups().map(headerGroup => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map(header => (
                  <th
                    key={header.id}
                    className="px-2 py-2 text-left text-xs font-medium uppercase tracking-wider"
                    onClick={header.column.getToggleSortingHandler()}
                    style={{
                      cursor: header.column.getCanSort() ? 'pointer' : 'default',
                      width: header.id === 'serialNumber' ? '60px' :
                            header.id === 'complaint_number' ? '120px' :
                            header.id === 'complainant_name' ? '150px' :
                            header.id === 'category' ? '120px' :
                            header.id === 'amount' ? '120px' :
                            header.id === 'status' ? '120px' :
                            header.id === 'actions' ? '120px' : '120px',
                      border: '0.5px solid var(--theme-border)',
                      backgroundColor: 'var(--theme-accent)',
                      color: 'var(--theme-text)',
                      height: '40px'
                    }}
                  >
                    <div className="flex items-center">
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext()
                      )}
                      {header.column.getIsSorted() && (
                        <span className="ml-1">
                          {header.column.getIsSorted() === 'asc' ? '↑' : '↓'}
                        </span>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody>
            {loading ? (
              // Loading state
              Array.from({ length: 5 }).map((_, index) => (
                <tr key={`loading-${index}`} className="animate-pulse">
                  {Array.from({ length: columns.length }).map((_, cellIndex) => (
                    <td
                      key={`loading-cell-${index}-${cellIndex}`}
                      className="px-2 py-2"
                      style={{
                        border: '0.5px solid var(--theme-border)'
                      }}
                    >
                      <div className="h-4 bg-gray-200 dark:bg-[rgba(0,170,255,0.15)] rounded w-3/4 dark:shadow-glow-blue-sm"></div>
                    </td>
                  ))}
                </tr>
              ))
            ) : complaints.length === 0 ? (
              // Empty state
              <tr>
                <td
                  colSpan={columns.length}
                  className="px-2 py-4 text-center text-gray-500 dark:text-gray-400"
                  style={{
                    border: '0.5px solid var(--theme-border)'
                  }}
                >
                  <div className="flex flex-col items-center justify-center space-y-4">
                    <FiAlertCircle className="w-10 h-10 text-gray-400 dark:text-primary-400 dark:opacity-60" />
                    <p>No complaints found</p>
                  </div>
                </td>
              </tr>
            ) : (
              // Data rows
              table.getRowModel().rows.map(row => (
                <tr key={row.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                  {row.getVisibleCells().map(cell => (
                    <td
                      key={cell.id}
                      className="px-2 py-2 text-sm"
                      style={{
                        border: '0.5px solid var(--theme-border)',
                        color: 'var(--theme-text)',
                        backgroundColor: 'var(--theme-bg-card)'
                      }}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="flex items-center justify-between px-2 py-2 mt-2 border-t border-gray-200 dark:border-gray-700">
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
            {pagination.total > 0 && (
              <>
                Showing <span className="font-medium mx-1">{(pagination.page - 1) * pagination.limit + 1}</span>
                to <span className="font-medium mx-1">
                  {Math.min(pagination.page * pagination.limit, pagination.total)}
                </span>
                of <span className="font-medium mx-1">{pagination.total}</span> results
              </>
            )}
          </div>

          <Pagination
            currentPage={pagination.page}
            totalPages={pagination.pages}
            onPageChange={onPageChange || (() => {})}
            size="sm"
          />
        </div>
      )}

      {/* Client-side pagination (when no pagination prop is provided) */}
      {!pagination && complaints.length > 0 && (
        <div className="flex items-center justify-center mt-2 space-x-2">
          <Button
            variant="outlined"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
            className="p-1 min-w-0"
          >
            <FiChevronLeft className="w-4 h-4" />
          </Button>

          <span className="text-sm text-gray-700 dark:text-gray-300">
            Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
          </span>

          <Button
            variant="outlined"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
            className="p-1 min-w-0"
          >
            <FiChevronRight className="w-4 h-4" />
          </Button>
        </div>
      )}
    </div>
  );
};

export default DashboardTable;
