import React from 'react';
import { ReactFlowInstance } from 'reactflow';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';
import { useAlert } from '../../context/TailwindAlertContext';
import { useThemeContext } from '../../context/TailwindThemeContext';
import { calculateDynamicLayout } from './DynamicLayoutManager';

interface GraphExporterProps {
  reactFlowRef: React.RefObject<HTMLDivElement | null>;
  isHorizontalLayout: boolean;
  reactFlowInstance: ReactFlowInstance | null;
}

/**
 * GraphExporter Component
 *
 * High-quality graph export with proper edge rendering and page separation
 */
const GraphExporter: React.FC<GraphExporterProps> = ({
  reactFlowRef,
  isHorizontalLayout: _isHorizontalLayout, // Prefixed with underscore to indicate it's not used directly
  reactFlowInstance
}) => {
  const { showError, showInfo, showSuccess } = useAlert();
  const { isDark } = useThemeContext();

  // Ask user for export preferences - simplified to only page layout
  const askExportPreferences = async () => {
    const modalContainer = document.createElement('div');
    modalContainer.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modalContainer.style.zIndex = '9999';

    modalContainer.innerHTML = `
      <div class="${isDark ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'} rounded-lg shadow-xl p-6 w-96 max-w-full">
        <h3 class="text-lg font-medium mb-4">Export Options</h3>
        <div class="mb-4">
          <label class="block text-sm font-medium ${isDark ? 'text-gray-300' : 'text-gray-700'} mb-1">Page Layout</label>
          <select id="export-layout" class="w-full border ${isDark ? 'border-gray-600 bg-gray-700 text-white' : 'border-gray-300 bg-white text-gray-800'} rounded-md px-3 py-2">
            <option value="single" selected>Single Page (Entire Graph)</option>
            <option value="multi">Multiple Pages (One Sender Per Page)</option>
          </select>
        </div>
        <div class="flex justify-end gap-3 mt-6">
          <button id="export-cancel" class="px-4 py-2 ${isDark ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-gray-200 text-gray-800 hover:bg-gray-300'} rounded-md">Cancel</button>
          <button id="export-confirm" class="px-4 py-2 ${isDark ? 'bg-green-800 text-white hover:bg-green-700' : 'bg-green-600 text-white hover:bg-green-700'} rounded-md">Export</button>
        </div>
      </div>
    `;

    document.body.appendChild(modalContainer);

    return new Promise<{ layout: string } | null>((resolve) => {
      const confirmButton = document.getElementById('export-confirm');
      const cancelButton = document.getElementById('export-cancel');

      const cleanup = () => {
        document.body.removeChild(modalContainer);
      };

      confirmButton?.addEventListener('click', () => {
        const layoutSelect = document.getElementById('export-layout') as HTMLSelectElement;

        const preferences = {
          layout: layoutSelect?.value || 'single'
        };

        cleanup();
        resolve(preferences);
      });

      cancelButton?.addEventListener('click', () => {
        cleanup();
        resolve(null);
      });

      modalContainer.addEventListener('click', (e) => {
        if (e.target === modalContainer) {
          cleanup();
          resolve(null);
        }
      });
    });
  };

  // Wait for SVG edges to be fully rendered
  const waitForEdges = async (container: HTMLElement, maxWait = 5000): Promise<void> => {
    const start = Date.now();
    const edges = container.querySelectorAll('.react-flow__edge path');
    return new Promise((resolve) => {
      const checkEdges = () => {
        let allRendered = true;
        edges.forEach((path) => {
          const svgPath = path as SVGPathElement;
          try {
            if (svgPath.getTotalLength() <= 0) allRendered = false;
          } catch (e) {
            allRendered = false;
          }
        });
        if (allRendered || Date.now() - start >= maxWait) resolve();
        else setTimeout(checkEdges, 100);
      };
      checkEdges();
    });
  };

  // Export the graph to PDF - completely revised for reliable capture
  const exportGraph = async () => {
    if (!reactFlowRef.current || !reactFlowInstance) {
      showError('Graph is not ready for export');
      return;
    }

    try {
      const preferences = await askExportPreferences();
      if (!preferences) return;

      showInfo('Preparing graph for export. This may take a moment...');

      // Store the current viewport and transform to restore later
      const originalViewport = reactFlowInstance.getViewport();
      const originalTransform = reactFlowInstance.toObject();

      // Get all nodes and edges
      const allNodes = reactFlowInstance.getNodes();
      const allEdges = reactFlowInstance.getEdges();

      // Get metadata node if available
      const metadataNode = allNodes.find(node => node.type === 'metadata');
      const complaintNumber = metadataNode?.data?.complaint_number || '';

      // Create a new jsPDF instance with landscape A4 format
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4',
        compress: true,
        hotfixes: ['px_scaling'] // Fix for better text quality
      });

      // Find sender nodes for potential multi-page export
      const senderNodes = allNodes.filter(node =>
        node.data?.layer === 0 || node.data?.isSenderNode === true
      );
      console.log('Sender nodes detected:', senderNodes.length, senderNodes);

      // CRITICAL: Make sure all nodes and edges are visible
      reactFlowInstance.setNodes(nodes =>
        nodes.map(node => ({ ...node, hidden: false }))
      );

      reactFlowInstance.setEdges(edges =>
        edges.map(edge => ({ ...edge, hidden: false }))
      );

      // CRITICAL: Properly arrange the graph before capture
      // First, reset the viewport to a neutral state
      reactFlowInstance.setViewport({ x: 0, y: 0, zoom: 1.0 });

      // Then fit the view to ensure all nodes are visible
      reactFlowInstance.fitView({
        padding: 0.2, // Moderate padding to ensure everything is visible
        includeHiddenNodes: false,
        duration: 0
      });

      // Wait for the view to update
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (preferences.layout === 'multi' && senderNodes.length > 1) {
        // Multi-page export - one sender per page with LR layout
        console.log('Using multi-page export for', senderNodes.length, 'senders with LR layout');

        // COMPLETELY DIFFERENT APPROACH: Process each sender separately with a clean state each time

        // Store the original state to restore later
        const originalViewport = reactFlowInstance.getViewport();
        const originalNodes = reactFlowInstance.getNodes();
        const originalEdges = reactFlowInstance.getEdges();

        // Get the current layout direction
        const reactFlowWrapper = reactFlowRef.current?.querySelector('.react-flow__container') as HTMLElement;
        const isHorizontalLayout = reactFlowWrapper?.classList.contains('horizontal');
        console.log('Current layout is horizontal:', isHorizontalLayout);

        // For each sender, create a separate page
        for (let i = 0; i < senderNodes.length; i++) {
          const senderNode = senderNodes[i];

          console.log(`Processing sender ${i+1}/${senderNodes.length}: ${senderNode.data?.account || senderNode.data?.label || 'Unknown'}`);

          // Add a new page if not the first sender
          if (i > 0) {
            pdf.addPage();
          }

          // CRITICAL: Start with a clean state for each sender
          // First, restore the original nodes and edges
          reactFlowInstance.setNodes(originalNodes);
          reactFlowInstance.setEdges(originalEdges);

          // Reset the viewport
          reactFlowInstance.setViewport({ x: 0, y: 0, zoom: 1.0 });

          // Wait for the state to update
          await new Promise(resolve => setTimeout(resolve, 500));

          // Find all nodes connected to this sender (directly or indirectly)
          const connectedNodeIds = new Set<string>([senderNode.id]);

          // Skip metadata node as requested
          // if (metadataNode) {
          //   connectedNodeIds.add(metadataNode.id);
          // }

          // Find all nodes that are descendants of this sender
          // First, get direct connections
          const directConnections = allEdges.filter(edge => edge.source === senderNode.id);
          directConnections.forEach(edge => connectedNodeIds.add(edge.target));

          // Then, recursively find all descendants
          let newNodesAdded = true;
          while (newNodesAdded) {
            newNodesAdded = false;
            allEdges.forEach(edge => {
              if (connectedNodeIds.has(edge.source) && !connectedNodeIds.has(edge.target)) {
                connectedNodeIds.add(edge.target);
                newNodesAdded = true;
              }
            });
          }

          console.log(`Found ${connectedNodeIds.size} nodes connected to this sender`);

          // CRITICAL: Create a new set of nodes and edges that only includes this sender's subgraph
          let senderSubgraphNodes = originalNodes
            .filter(node => connectedNodeIds.has(node.id))
            .map(node => ({...node, hidden: false}));

          const senderSubgraphEdges = originalEdges
            .filter(edge =>
              connectedNodeIds.has(edge.source) && connectedNodeIds.has(edge.target)
            )
            .map(edge => ({...edge, hidden: false}));

          // CRITICAL: Implement row-splitting algorithm for LR layout
          // If any row in the sender's child tree has more than 20 nodes, split it into multiple rows
          // First, organize nodes by layer/depth
          const nodesByLayer: { [key: number]: any[] } = {};

          // Start with the sender node at layer 0
          nodesByLayer[0] = [senderNode];

          // Group all other nodes by their layer
          senderSubgraphNodes.forEach(node => {
            if (node.id !== senderNode.id) {
              const layer = node.data?.layer || 0;
              if (!nodesByLayer[layer]) {
                nodesByLayer[layer] = [];
              }
              nodesByLayer[layer].push(node);
            }
          });

          console.log('Nodes by layer before splitting:', Object.keys(nodesByLayer).map(layer =>
            `Layer ${layer}: ${nodesByLayer[parseInt(layer)].length} nodes`
          ));

          // Check if any layer has more than 20 nodes and split if needed
          const MAX_NODES_PER_ROW = 20;
          const newNodesByLayer: { [key: number]: any[] } = {};
          let maxLayer = 0;

          Object.keys(nodesByLayer).forEach(layerStr => {
            const layer = parseInt(layerStr);
            const nodesInLayer = nodesByLayer[layer];

            if (nodesInLayer.length <= MAX_NODES_PER_ROW) {
              // No splitting needed
              newNodesByLayer[layer] = nodesInLayer;
            } else {
              // Split this layer into multiple rows
              const numRows = Math.ceil(nodesInLayer.length / MAX_NODES_PER_ROW);
              console.log(`Splitting layer ${layer} with ${nodesInLayer.length} nodes into ${numRows} rows`);

              // Group nodes by their parent to maintain hierarchy
              const nodesByParent: { [key: string]: any[] } = {};

              nodesInLayer.forEach(node => {
                // Find the parent node by looking at incoming edges
                const parentEdges = senderSubgraphEdges.filter(edge => edge.target === node.id);
                if (parentEdges.length > 0) {
                  const parentId = parentEdges[0].source;
                  if (!nodesByParent[parentId]) {
                    nodesByParent[parentId] = [];
                  }
                  nodesByParent[parentId].push(node);
                } else {
                  // No parent found, use a default group
                  if (!nodesByParent['default']) {
                    nodesByParent['default'] = [];
                  }
                  nodesByParent['default'].push(node);
                }
              });

              // Distribute nodes across rows while keeping children of the same parent together
              let currentRow = 0;
              let nodesInCurrentRow = 0;

              Object.keys(nodesByParent).forEach(parentId => {
                const childNodes = nodesByParent[parentId];

                // If adding these nodes would exceed the limit, move to next row
                if (nodesInCurrentRow + childNodes.length > MAX_NODES_PER_ROW && nodesInCurrentRow > 0) {
                  currentRow++;
                  nodesInCurrentRow = 0;
                }

                // Calculate the new layer for these nodes
                const newLayer = layer + (currentRow * 0.1); // Use decimal to maintain order

                // Add nodes to the new layer
                if (!newNodesByLayer[newLayer]) {
                  newNodesByLayer[newLayer] = [];
                }

                // Add all child nodes to this row
                childNodes.forEach(node => {
                  // Update the node's position for the new row
                  const updatedNode = {
                    ...node,
                    data: {
                      ...node.data,
                      originalLayer: node.data?.layer, // Store original layer
                      layer: newLayer // Update layer for rendering
                    }
                  };
                  newNodesByLayer[newLayer].push(updatedNode);
                });

                nodesInCurrentRow += childNodes.length;
              });

              // Update max layer
              maxLayer = Math.max(maxLayer, layer + (currentRow * 0.1));
            }
          });

          console.log('Nodes by layer after splitting:', Object.keys(newNodesByLayer).map(layer =>
            `Layer ${layer}: ${newNodesByLayer[parseFloat(layer)].length} nodes`
          ));

          // Flatten the nodes back into a single array
          senderSubgraphNodes = Object.values(newNodesByLayer).flat();

          // Check if this sender has more than 20 child nodes
          const hasMoreThan20Children = senderSubgraphNodes.filter(node =>
            node.data?.parentId === senderNode.id
          ).length > 20;

          // CRITICAL: Use DynamicLayoutManager to properly position nodes
          // This ensures child nodes appear directly below their parent nodes
          const nodesWithProperLayout = calculateDynamicLayout(
            senderSubgraphNodes,
            senderSubgraphEdges,
            {
              isHorizontalLayout: hasMoreThan20Children, // Use horizontal layout only if more than 20 children
              animate: false
            }
          );

          // CRITICAL: Replace all nodes and edges with just this sender's subgraph
          // This is different from hiding nodes - we're actually removing all other nodes
          reactFlowInstance.setNodes(nodesWithProperLayout);
          reactFlowInstance.setEdges(senderSubgraphEdges);

          // Wait for the state to update
          await new Promise(resolve => setTimeout(resolve, 1000));

          // CRITICAL: Set layout to LR (horizontal) for multi-page export
          // This is done by adding the 'horizontal' class to the container
          if (!isHorizontalLayout && reactFlowWrapper) {
            reactFlowWrapper.classList.add('horizontal');

            // Wait for the layout to update
            await new Promise(resolve => setTimeout(resolve, 500));
          }

          // Fit view to show only this sender's subgraph
          reactFlowInstance.fitView({
            padding: 0.2,
            includeHiddenNodes: false,
            duration: 0
          });

          // Wait for the view to update
          await new Promise(resolve => setTimeout(resolve, 1000));

          console.log(`Capturing sender ${i+1} subgraph with ${senderSubgraphNodes.length} nodes and ${senderSubgraphEdges.length} edges`);

          // Capture this sender's subgraph
          await captureAndAddToPage(
            pdf,
            senderSubgraphNodes,
            senderSubgraphEdges,
            reactFlowRef.current,
            reactFlowInstance,
            complaintNumber,
            `Sender: ${senderNode.data?.account || senderNode.data?.label || 'Unknown'} (${i+1}/${senderNodes.length})`,
            5.0, // High quality
            false // No title
          );

          // CRITICAL: Wait between pages to ensure cleanup is complete
          await new Promise(resolve => setTimeout(resolve, 1000));
        }

        // Restore the original state after all pages are processed
        reactFlowInstance.setNodes(originalNodes);
        reactFlowInstance.setEdges(originalEdges);
        reactFlowInstance.setViewport(originalViewport);

        // Restore original layout direction
        if (!isHorizontalLayout && reactFlowWrapper) {
          reactFlowWrapper.classList.remove('horizontal');
        }

        // Fit view to restore the original layout
        reactFlowInstance.fitView({
          padding: 0.2,
          includeHiddenNodes: false,
          duration: 0
        });
      } else {
        // Single page export - capture the entire graph
        console.log('Using single page export for entire graph');

        // CRITICAL: Make sure all nodes and edges are visible
        const visibleNodes = allNodes.map(node => ({...node, hidden: false}));
        const visibleEdges = allEdges.map(edge => ({...edge, hidden: false}));

        // Check if any sender node has more than 20 child nodes
        const senderNodes = visibleNodes.filter(node =>
          node.data?.layer === 0 && node.type !== 'metadata'
        );

        let useHorizontalLayout = _isHorizontalLayout;

        // Check each sender node for more than 20 children
        for (const senderNode of senderNodes) {
          const childCount = visibleNodes.filter(node =>
            node.data?.parentId === senderNode.id
          ).length;

          if (childCount > 20) {
            useHorizontalLayout = true;
            break;
          }
        }

        // CRITICAL: Use DynamicLayoutManager to properly position nodes
        // This ensures child nodes appear directly below their parent nodes
        const nodesWithProperLayout = calculateDynamicLayout(
          visibleNodes,
          visibleEdges,
          {
            isHorizontalLayout: useHorizontalLayout,
            animate: false
          }
        );

        // Set the nodes with proper layout and edges
        reactFlowInstance.setNodes(nodesWithProperLayout);
        reactFlowInstance.setEdges(visibleEdges);

        // CRITICAL: Reset the viewport and fit view
        reactFlowInstance.setViewport({ x: 0, y: 0, zoom: 1.0 });

        // Wait for the state to update
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Fit view to ensure all nodes are visible
        reactFlowInstance.fitView({
          padding: 0.2,
          includeHiddenNodes: false,
          duration: 0
        });

        // Wait for the view to update and edges to render
        await new Promise(resolve => setTimeout(resolve, 2000));
        await waitForEdges(reactFlowRef.current);

        // Capture the entire graph
        await captureAndAddToPage(
          pdf,
          visibleNodes,
          visibleEdges,
          reactFlowRef.current,
          reactFlowInstance,
          complaintNumber,
          undefined, // No title
          5.0, // High quality
          false // No title
        );
      }

      // Restore original state
      reactFlowInstance.setNodes(originalTransform.nodes);
      reactFlowInstance.setEdges(originalTransform.edges);
      reactFlowInstance.setViewport(originalViewport);

      // Save the PDF
      const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
      const filename = complaintNumber
        ? `transaction-flow-${complaintNumber}.pdf`
        : `transaction-flow-${timestamp}.pdf`;

      pdf.save(filename);

      showSuccess('Graph exported successfully!');
    } catch (error) {
      console.error('Error exporting graph:', error);
      showError('Failed to export graph: ' + (error instanceof Error ? error.message : String(error)));

      // Attempt to restore the graph state
      try {
        reactFlowInstance.fitView({ padding: 0.3 });
      } catch (restoreError) {
        console.error('Error restoring graph state:', restoreError);
      }
    }
  };

  // Capture the graph and add it to the PDF - improved version with better edge rendering
  const captureAndAddToPage = async (
    pdf: jsPDF,
    nodes: any[],
    edges: any[],
    container: HTMLElement,
    reactFlowInstance: ReactFlowInstance,
    _complaintNumber: string = '', // Prefixed with underscore to indicate it's not used directly
    pageTitle?: string,
    qualityScale: number = 5.0, // Always use high quality
    showTitle: boolean = false // No title by default
  ) => {
    try {
      console.log(`Capturing graph with ${nodes.length} nodes and ${edges.length} edges`);

      // Get the ReactFlow container
      const reactFlowContainer = container.querySelector('.react-flow') as HTMLElement;
      if (!reactFlowContainer) throw new Error('Could not find ReactFlow container');

      // Get the viewport element
      const viewport = reactFlowContainer.querySelector('.react-flow__viewport') as HTMLElement;
      if (!viewport) throw new Error('Could not find ReactFlow viewport');

      // Get the current viewport transform
      const currentTransform = reactFlowInstance.getViewport();
      console.log('Current transform before capture:', currentTransform);

      // Apply the transform directly to the viewport element
      viewport.style.transform = `translate(${currentTransform.x}px, ${currentTransform.y}px) scale(${currentTransform.zoom})`;

      // Make sure all nodes are visible in the DOM with enhanced visibility
      const nodeElements = reactFlowContainer.querySelectorAll('.react-flow__node');
      nodeElements.forEach((nodeEl: Element) => {
        (nodeEl as HTMLElement).style.display = 'block';
        (nodeEl as HTMLElement).style.visibility = 'visible';
        (nodeEl as HTMLElement).style.opacity = '1';
        (nodeEl as HTMLElement).style.zIndex = '5'; // Ensure nodes are visible
      });

      // SIMPLIFIED APPROACH: Make edges visible by directly modifying the SVG styles
      // This avoids creating duplicate elements that might cause shadows
      const edgeElements = reactFlowContainer.querySelectorAll('.react-flow__edge');
      console.log(`Enhancing ${edgeElements.length} edge elements`);

      edgeElements.forEach((edgeEl: Element) => {
        // Make edge visible
        (edgeEl as HTMLElement).style.display = 'block';
        (edgeEl as HTMLElement).style.visibility = 'visible';
        (edgeEl as HTMLElement).style.opacity = '1';
        (edgeEl as HTMLElement).style.zIndex = '1000'; // Very high z-index to ensure visibility

        // Find and enhance all paths in this edge
        const paths = edgeEl.querySelectorAll('path');
        console.log(`Found ${paths.length} paths in edge ${edgeEl.getAttribute('data-testid')}`);

        paths.forEach(path => {
          // Save original attributes to restore later
          const originalStroke = path.getAttribute('stroke');
          const originalStrokeWidth = path.getAttribute('stroke-width');

          // Set attributes directly on the path
          path.setAttribute('stroke', '#3b82f6'); // Theme color
          path.setAttribute('stroke-width', '8'); // Thicker lines for better visibility
          path.setAttribute('fill', 'none');
          path.setAttribute('stroke-dasharray', ''); // Ensure solid lines
          path.setAttribute('stroke-linecap', 'round');
          path.setAttribute('stroke-linejoin', 'round');

          // Store original values as data attributes for restoration
          path.dataset.originalStroke = originalStroke || '';
          path.dataset.originalStrokeWidth = originalStrokeWidth || '';

          // Make sure the path is visible
          (path as SVGPathElement).style.display = 'block';
          (path as SVGPathElement).style.visibility = 'visible';
          (path as SVGPathElement).style.opacity = '1';
        });

        // Enhance markers (arrows)
        const markers = edgeEl.querySelectorAll('marker');
        markers.forEach(marker => {
          marker.setAttribute('fill', '#3b82f6');
          marker.setAttribute('markerWidth', '20'); // Larger marker
          marker.setAttribute('markerHeight', '20'); // Larger marker
        });
      });

      // CRITICAL: Draw direct connections between nodes using the React Flow data
      // This ensures we have visible connections even if the SVG paths are not rendering correctly
      try {
        // Create a canvas element to draw the connections
        const canvas = document.createElement('canvas');
        canvas.width = reactFlowContainer.offsetWidth;
        canvas.height = reactFlowContainer.offsetHeight;
        canvas.style.position = 'absolute';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.pointerEvents = 'none';
        canvas.style.zIndex = '500'; // Below nodes but above other elements

        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.strokeStyle = '#3b82f6';
          ctx.lineWidth = 3;
          ctx.lineCap = 'round';
          ctx.lineJoin = 'round';

          // Draw connections based on the edges data
          edges.forEach(edge => {
            const sourceNode = nodes.find(node => node.id === edge.source);
            const targetNode = nodes.find(node => node.id === edge.target);

            if (sourceNode && targetNode) {
              const sourceElement = reactFlowContainer.querySelector(`[data-id="${sourceNode.id}"]`) as HTMLElement;
              const targetElement = reactFlowContainer.querySelector(`[data-id="${targetNode.id}"]`) as HTMLElement;

              if (sourceElement && targetElement) {
                const sourceRect = sourceElement.getBoundingClientRect();
                const targetRect = targetElement.getBoundingClientRect();

                // Calculate positions relative to the container
                const containerRect = reactFlowContainer.getBoundingClientRect();

                const sourceX = sourceRect.left + sourceRect.width / 2 - containerRect.left;
                const sourceY = sourceRect.top + sourceRect.height - containerRect.top;
                const targetX = targetRect.left + targetRect.width / 2 - containerRect.left;
                const targetY = targetRect.top - containerRect.top;

                // Draw a simple curved line
                ctx.beginPath();
                ctx.moveTo(sourceX, sourceY);

                // Check if we're using horizontal layout (LR) or vertical layout (TB)
                const isHorizontalLayout = reactFlowContainer.classList.contains('horizontal');

                if (isHorizontalLayout) {
                  // For LR layout, use horizontal curve
                  const midX = (sourceX + targetX) / 2;
                  ctx.bezierCurveTo(
                    midX, sourceY, // First control point
                    midX, targetY, // Second control point
                    targetX, targetY // End point
                  );
                } else {
                  // For TB layout, use vertical curve
                  const midY = (sourceY + targetY) / 2;
                  ctx.bezierCurveTo(
                    sourceX, midY, // First control point
                    targetX, midY, // Second control point
                    targetX, targetY // End point
                  );
                }

                ctx.stroke();

                // Draw an arrow at the target
                const arrowSize = 8;
                let angle;

                if (isHorizontalLayout) {
                  // For LR layout, calculate angle based on horizontal approach
                  const midX = (sourceX + targetX) / 2;
                  angle = Math.atan2(targetY - sourceY, targetX - midX);
                } else {
                  // For TB layout, calculate angle based on vertical approach
                  const midY = (sourceY + targetY) / 2;
                  angle = Math.atan2(targetY - midY, targetX - sourceX);
                }

                ctx.beginPath();
                ctx.moveTo(targetX, targetY);
                ctx.lineTo(
                  targetX - arrowSize * Math.cos(angle - Math.PI / 6),
                  targetY - arrowSize * Math.sin(angle - Math.PI / 6)
                );
                ctx.lineTo(
                  targetX - arrowSize * Math.cos(angle + Math.PI / 6),
                  targetY - arrowSize * Math.sin(angle + Math.PI / 6)
                );
                ctx.closePath();
                ctx.fillStyle = '#3b82f6';
                ctx.fill();
              }
            }
          });
        }

        // Add the canvas to the container
        reactFlowContainer.appendChild(canvas);

        // Store a reference to remove it later
        reactFlowContainer.dataset.edgeCanvas = 'true';
      } catch (canvasError) {
        console.error('Error creating edge canvas:', canvasError);
      }

      // Wait for edges to be fully rendered
      await waitForEdges(reactFlowContainer);

      // Wait longer for styles to apply
      await new Promise(resolve => setTimeout(resolve, 1500));

      // CRITICAL: Remove any existing canvas or SVG elements before creating new ones
      // This prevents shadow duplicates
      try {
        // Remove any canvas elements
        const existingCanvases = reactFlowContainer.querySelectorAll('canvas');
        existingCanvases.forEach(canvas => {
          reactFlowContainer.removeChild(canvas);
        });

        // Remove any custom SVG elements
        const existingSvgs = reactFlowContainer.querySelectorAll('.edge-overlay-svg');
        existingSvgs.forEach(svg => {
          reactFlowContainer.removeChild(svg);
        });

        delete reactFlowContainer.dataset.edgeCanvas;
      } catch (cleanupError) {
        console.error('Error cleaning up existing elements:', cleanupError);
      }

      // CRITICAL: Hide the ReactFlow background pattern to avoid shadows
      const reactFlowBg = reactFlowContainer.querySelector('.react-flow__background');
      if (reactFlowBg) {
        (reactFlowBg as HTMLElement).style.display = 'none';
      }

      // CRITICAL: Capture the entire ReactFlow container, not just the viewport
      // This ensures we get all nodes, even if they're outside the current viewport
      const elementToCapture = reactFlowContainer;
      console.log('Capturing element with dimensions:', elementToCapture.offsetWidth, 'x', elementToCapture.offsetHeight);

      // First, make sure all nodes and edges are visible
      const allNodes = elementToCapture.querySelectorAll('.react-flow__node');
      const allEdges = elementToCapture.querySelectorAll('.react-flow__edge');

      console.log(`Found ${allNodes.length} nodes and ${allEdges.length} edges to capture`);

      // CRITICAL: Reset all styles first to avoid duplicates/shadows
      allNodes.forEach((node: Element) => {
        (node as HTMLElement).style.display = '';
        (node as HTMLElement).style.visibility = '';
        (node as HTMLElement).style.opacity = '';
        (node as HTMLElement).style.zIndex = '';
      });

      allEdges.forEach((edge: Element) => {
        (edge as HTMLElement).style.display = '';
        (edge as HTMLElement).style.visibility = '';
        (edge as HTMLElement).style.opacity = '';
        (edge as HTMLElement).style.zIndex = '';

        // Reset path styles
        const paths = edge.querySelectorAll('path');
        paths.forEach(path => {
          path.removeAttribute('stroke-width');
          path.removeAttribute('stroke');
          path.removeAttribute('fill');
          path.removeAttribute('stroke-dasharray');
          path.removeAttribute('stroke-linecap');
          path.removeAttribute('stroke-linejoin');
        });
      });

      // Force a repaint after resetting styles
      elementToCapture.style.transform = elementToCapture.style.transform;
      await new Promise(resolve => setTimeout(resolve, 300));

      // Now make all nodes visible with clean styles and add white background to fix shadows
      allNodes.forEach((node: Element) => {
        const nodeEl = node as HTMLElement;
        nodeEl.style.display = 'block';
        nodeEl.style.visibility = 'visible';
        nodeEl.style.opacity = '1';
        nodeEl.style.zIndex = '5';

        // CRITICAL: Add white background to each node to prevent shadows
        nodeEl.style.backgroundColor = 'white';
        nodeEl.style.boxShadow = 'none'; // Remove any box shadows

        // Also add white background to any child elements that might be causing shadows
        const childElements = nodeEl.querySelectorAll('*');
        childElements.forEach((child: Element) => {
          const childEl = child as HTMLElement;
          if (childEl.style) {
            childEl.style.backgroundColor = 'white';
            childEl.style.boxShadow = 'none';
          }
        });
      });

      // Make all edges visible with clean styles
      allEdges.forEach((edge: Element) => {
        (edge as HTMLElement).style.display = 'block';
        (edge as HTMLElement).style.visibility = 'visible';
        (edge as HTMLElement).style.opacity = '1';
        (edge as HTMLElement).style.zIndex = '10';
      });

      // CRITICAL: First, hide all existing edges to prevent them from being captured
      allEdges.forEach((edge: Element) => {
        (edge as HTMLElement).style.display = 'none';
        (edge as HTMLElement).style.visibility = 'hidden';
        (edge as HTMLElement).style.opacity = '0';
      });

      // CRITICAL: Create a new SVG overlay for edges that will be captured in the PDF
      // This ensures we have complete control over the edge appearance
      try {
        // Create an SVG element that will overlay the entire graph
        const svgOverlay = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svgOverlay.setAttribute('width', elementToCapture.offsetWidth.toString());
        svgOverlay.setAttribute('height', elementToCapture.offsetHeight.toString());
        svgOverlay.style.position = 'absolute';
        svgOverlay.style.top = '0';
        svgOverlay.style.left = '0';
        svgOverlay.style.pointerEvents = 'none';
        svgOverlay.style.zIndex = '1000';
        svgOverlay.classList.add('edge-overlay-svg');

        // CRITICAL: Add a style element to ensure consistent rendering
        const styleElement = document.createElementNS('http://www.w3.org/2000/svg', 'style');
        styleElement.textContent = `
          .edge-path {
            stroke: #3b82f6;
            stroke-width: 1.5px;
            fill: none;
            stroke-linecap: round;
            stroke-linejoin: round;
          }
          .edge-arrow {
            fill: #3b82f6;
          }
        `;
        svgOverlay.appendChild(styleElement);

        // Draw connections based on the edges data
        edges.forEach(edge => {
          const sourceNode = nodes.find(node => node.id === edge.source);
          const targetNode = nodes.find(node => node.id === edge.target);

          if (sourceNode && targetNode) {
            const sourceElement = reactFlowContainer.querySelector(`[data-id="${sourceNode.id}"]`) as HTMLElement;
            const targetElement = reactFlowContainer.querySelector(`[data-id="${targetNode.id}"]`) as HTMLElement;

            if (sourceElement && targetElement) {
              const sourceRect = sourceElement.getBoundingClientRect();
              const targetRect = targetElement.getBoundingClientRect();

              // Calculate positions relative to the container
              const containerRect = reactFlowContainer.getBoundingClientRect();

              const sourceX = sourceRect.left + sourceRect.width / 2 - containerRect.left;
              const sourceY = sourceRect.top + sourceRect.height - containerRect.top;
              const targetX = targetRect.left + targetRect.width / 2 - containerRect.left;
              const targetY = targetRect.top - containerRect.top;

              // Create a group for this edge
              const edgeGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');

              // Create a path element for the edge
              const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');

              // Calculate control points for a smooth curve
              const midY = (sourceY + targetY) / 2;

              // Create a bezier curve path
              const pathData = `M${sourceX},${sourceY} C${sourceX},${midY} ${targetX},${midY} ${targetX},${targetY}`;

              path.setAttribute('d', pathData);
              path.setAttribute('class', 'edge-path');
              // Explicitly set these attributes to override any CSS
              path.setAttribute('stroke', '#3b82f6');
              path.setAttribute('stroke-width', '1.5'); // Even thinner for better PDF rendering
              path.setAttribute('fill', 'none');

              edgeGroup.appendChild(path);

              // Create an arrow marker at the target - smaller to match thinner lines
              const arrow = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
              const arrowSize = 5; // Even smaller arrow size
              const angle = Math.atan2(targetY - midY, targetX - sourceX);

              arrow.setAttribute('points',
                `${targetX},${targetY} ` +
                `${targetX - arrowSize * Math.cos(angle - Math.PI / 6)},${targetY - arrowSize * Math.sin(angle - Math.PI / 6)} ` +
                `${targetX - arrowSize * Math.cos(angle + Math.PI / 6)},${targetY - arrowSize * Math.sin(angle + Math.PI / 6)}`
              );
              arrow.setAttribute('class', 'edge-arrow');
              arrow.setAttribute('fill', '#3b82f6');

              edgeGroup.appendChild(arrow);
              svgOverlay.appendChild(edgeGroup);
            }
          }
        });

        // Add the SVG overlay to the container
        reactFlowContainer.appendChild(svgOverlay);

        // Force a repaint to ensure the SVG is rendered
        reactFlowContainer.style.transform = reactFlowContainer.style.transform;

        // Wait for the SVG to render
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (svgError) {
        console.error('Error creating SVG overlay:', svgError);
      }

      // Force another repaint to ensure all elements are visible with new styles
      elementToCapture.style.transform = elementToCapture.style.transform;

      // Wait for the repaint to complete
      await new Promise(resolve => setTimeout(resolve, 500));

      // Use improved html2canvas settings for better quality
      const canvas = await html2canvas(elementToCapture, {
        backgroundColor: 'white',
        scale: qualityScale, // High quality scale
        logging: true, // Enable logging for debugging
        allowTaint: true,
        useCORS: true,
        imageTimeout: 60000, // Longer timeout for complex graphs
        onclone: (clonedDoc) => {
          console.log('html2canvas onclone called');

          // Make sure all nodes are visible in the cloned document
          const clonedNodes = clonedDoc.querySelectorAll('.react-flow__node');
          console.log(`Found ${clonedNodes.length} nodes in cloned document`);

          clonedNodes.forEach((nodeEl: Element) => {
            (nodeEl as HTMLElement).style.display = 'block';
            (nodeEl as HTMLElement).style.visibility = 'visible';
            (nodeEl as HTMLElement).style.opacity = '1';
            (nodeEl as HTMLElement).style.zIndex = '5';
          });

          // Make all edges visible in the cloned document
          const clonedEdges = clonedDoc.querySelectorAll('.react-flow__edge');
          console.log(`Found ${clonedEdges.length} edges in cloned document`);

          clonedEdges.forEach((edgeEl: Element) => {
            (edgeEl as HTMLElement).style.display = 'block';
            (edgeEl as HTMLElement).style.visibility = 'visible';
            (edgeEl as HTMLElement).style.opacity = '1';
            (edgeEl as HTMLElement).style.zIndex = '10';

            // Enhance all paths in the cloned document
            const paths = edgeEl.querySelectorAll('path');
            paths.forEach(path => {
              path.setAttribute('stroke-width', '12'); // Even thicker for export
              path.setAttribute('stroke', '#3b82f6');
              path.setAttribute('fill', 'none');
              path.setAttribute('stroke-dasharray', '');
              path.setAttribute('stroke-linecap', 'round');
              path.setAttribute('stroke-linejoin', 'round');
            });
          });

          // Also clone any enhanced edges we added
          const enhancedEdges = clonedDoc.querySelector('.enhanced-edges');
          if (enhancedEdges) {
            (enhancedEdges as HTMLElement).style.display = 'block';
            (enhancedEdges as HTMLElement).style.visibility = 'visible';
            (enhancedEdges as HTMLElement).style.opacity = '1';
            (enhancedEdges as HTMLElement).style.zIndex = '2000';
          }

          // CRITICAL: Force a repaint in the cloned document
          const viewport = clonedDoc.querySelector('.react-flow__viewport') as HTMLElement;
          if (viewport) {
            viewport.style.transform = viewport.style.transform;
          }
        }
      });

      console.log(`Canvas dimensions: ${canvas.width}x${canvas.height}`);

      // Calculate dimensions to fit the PDF page
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      // Only add title if showTitle is true
      if (showTitle && pageTitle) {
        // Use custom page title if provided
        pdf.setFontSize(12);
        pdf.setTextColor(100, 100, 100);
        pdf.text(
          pageTitle,
          pageWidth / 2,
          10,
          { align: 'center' }
        );
      }

      // CRITICAL: Use minimal margins to maximize graph space
      const topMargin = 2; // Minimal top margin
      const bottomMargin = 2; // Minimal bottom margin
      const sideMargin = 2; // Minimal side margins

      // Calculate available space
      const availableWidth = pageWidth - (2 * sideMargin);
      const availableHeight = pageHeight - topMargin - bottomMargin;

      // Calculate optimal dimensions to maintain aspect ratio
      const canvasAspectRatio = canvas.width / canvas.height;

      // CRITICAL: Always maximize the available space
      let imgWidth = availableWidth;
      let imgHeight = availableHeight;

      // Adjust dimensions to maintain aspect ratio while maximizing space
      if (canvasAspectRatio > availableWidth / availableHeight) {
        // Image is wider than available space (relative to height)
        imgHeight = imgWidth / canvasAspectRatio;
      } else {
        // Image is taller than available space (relative to width)
        imgWidth = imgHeight * canvasAspectRatio;
      }

      // CRITICAL: Center the image horizontally and position at top
      // This ensures we use the maximum available space
      const xOffset = sideMargin + (availableWidth - imgWidth) / 2;
      const yOffset = topMargin;

      console.log(`Page dimensions: ${pageWidth}x${pageHeight}`);
      console.log(`Available space: ${availableWidth}x${availableHeight}`);
      console.log(`Canvas aspect ratio: ${canvasAspectRatio}`);
      console.log(`Image dimensions: ${imgWidth}x${imgHeight}, positioned at (${xOffset}, ${yOffset})`);

      // Add the image to the PDF with proper positioning
      // Use JPEG format for better memory efficiency with large graphs
      pdf.addImage(
        canvas.toDataURL('image/jpeg', 1.0), // Full quality
        'JPEG',
        xOffset,
        yOffset,
        imgWidth,
        imgHeight
      );

      // Add a small note at the bottom of the page
      pdf.setFontSize(8);
      pdf.setTextColor(150, 150, 150);
      pdf.text('Generated by Cyber Sakha', pageWidth - 40, pageHeight - 3);

      // CRITICAL: Clean up - remove any SVG overlays and restore background
      try {
        // Remove any SVG overlays
        const svgOverlays = reactFlowContainer.querySelectorAll('.edge-overlay-svg');
        svgOverlays.forEach(svg => {
          reactFlowContainer.removeChild(svg);
        });

        // Remove any canvas elements
        const canvases = reactFlowContainer.querySelectorAll('canvas');
        canvases.forEach(canvas => {
          reactFlowContainer.removeChild(canvas);
        });

        // Restore the ReactFlow background
        const reactFlowBg = reactFlowContainer.querySelector('.react-flow__background');
        if (reactFlowBg) {
          (reactFlowBg as HTMLElement).style.display = '';
        }

        delete reactFlowContainer.dataset.edgeCanvas;
      } catch (cleanupError) {
        console.error('Error cleaning up elements:', cleanupError);
      }

      // Restore original styles for nodes
      allNodes.forEach((nodeEl: Element) => {
        (nodeEl as HTMLElement).style.display = '';
        (nodeEl as HTMLElement).style.visibility = '';
        (nodeEl as HTMLElement).style.opacity = '';
        (nodeEl as HTMLElement).style.zIndex = '';
      });

      // Restore original styles for edges - make them visible again
      allEdges.forEach((edgeEl: Element) => {
        (edgeEl as HTMLElement).style.display = '';
        (edgeEl as HTMLElement).style.visibility = '';
        (edgeEl as HTMLElement).style.opacity = '';
        (edgeEl as HTMLElement).style.zIndex = '';

        // Restore original path styles using saved data attributes
        const paths = edgeEl.querySelectorAll('path');
        paths.forEach(path => {
          // Get original values from data attributes
          const originalStroke = path.dataset.originalStroke;
          const originalStrokeWidth = path.dataset.originalStrokeWidth;

          if (originalStroke) {
            path.setAttribute('stroke', originalStroke);
          } else {
            path.removeAttribute('stroke');
          }

          if (originalStrokeWidth) {
            path.setAttribute('stroke-width', originalStrokeWidth);
          } else {
            path.removeAttribute('stroke-width');
          }

          // Remove other attributes we added
          path.removeAttribute('fill');
          path.removeAttribute('stroke-dasharray');
          path.removeAttribute('stroke-linecap');
          path.removeAttribute('stroke-linejoin');

          // Remove data attributes
          delete path.dataset.originalStroke;
          delete path.dataset.originalStrokeWidth;
        });
      });

    } catch (error) {
      console.error('Error capturing graph:', error);

      // Add a fallback text message
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();

      pdf.setFontSize(16);
      pdf.setTextColor(0, 0, 0);
      pdf.text('Failed to capture the graph.', pageWidth / 2, pageHeight / 2 - 20, { align: 'center' });
      pdf.setFontSize(12);
      pdf.text('Please try again with a different browser or with fewer nodes.', pageWidth / 2, pageHeight / 2, { align: 'center' });
      pdf.text(`Error: ${error instanceof Error ? error.message : String(error)}`, pageWidth / 2, pageHeight / 2 + 20, { align: 'center' });
    }
  };

  return (
    <button
      className={`px-3 py-1.5 ${isDark ? 'bg-green-800 hover:bg-green-700' : 'bg-green-600 hover:bg-green-700'} text-white rounded-md transition-colors flex items-center gap-2 text-sm`}
      onClick={exportGraph}
      title="Export complete graph to PDF with all nodes and connections"
    >
      <svg xmlns="http://www.w3.org/2000/svg" className="w-3.5 h-3.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
      </svg>
      Export Graph
    </button>
  );
};

export default GraphExporter;
