import React, { useState, useCallback, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FiArrowLeft, FiFileText, FiBarChart2, FiDownload } from 'react-icons/fi';
import { useAlert } from '../context/TailwindAlertContext';
import ReactTableCSVPreview from '../components/ReactTableCSVPreview';
import Button from '../components/ui/Button';
import { withLayout } from '../context/LayoutContext';
import { useComplaintDetail, useComplaintCSVData } from '../hooks/useComplaintsQuery';
import { useUpdateCSVData } from '../hooks/useComplaintMutations';
import complaintService from '../services/complaintService';

const ComplaintDetailComponent: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { showError, showSuccess } = useAlert();

  // React Query hooks for data fetching
  const {
    data: complaintData,
    isLoading: complaintLoading,
    error: complaintError,
    refetch: refetchComplaint
  } = useComplaintDetail(id);

  const {
    data: csvDataResponse,
    isLoading: csvLoading,
    error: csvError,
    refetch: refetchCSV
  } = useComplaintCSVData(id);

  // Mutation for updating CSV data
  const updateCSVMutation = useUpdateCSVData();

  // Local state
  const [isDownloading, setIsDownloading] = useState(false);

  // Process CSV data from the response
  const csvData = useMemo(() => {
    if (!csvDataResponse?.csv_data_base64) {
      // Fallback: generate CSV from transactions if available
      if (complaintData?.transactions && Array.isArray(complaintData.transactions) && complaintData.transactions.length > 0) {
        const headers = [
          'Layer', 'Fraud Type', 'Sender Account', 'Sender Txn ID', 'Sender Bank',
          'Receiver Account', 'Txn ID', 'Receiver Bank', 'Txn Type', 'Txn Date', 'Amount', 'Reference'
        ];

        let csvContent = headers.join(',') + '\n';

        complaintData.transactions.forEach((txn: any) => {
          const row = [
            txn.layer || '',
            txn.fraud_type || 'banking_upi',
            txn.sender_account || '',
            txn.sender_transaction_id || '',
            txn.sender_bank || '',
            txn.receiver_account || '',
            txn.receiver_transaction_id || txn.txn_id || '',
            txn.receiver_bank || '',
            txn.type || txn.txn_type || '',
            txn.date || txn.txn_date || '',
            txn.amount || '',
            txn.reference || txn.receiver_info || ''
          ];

          const escapedRow = row.map(field => {
            if (field && field.toString().includes(',')) {
              return `"${field}"`;
            }
            return field;
          });

          csvContent += escapedRow.join(',') + '\n';
        });

        return csvContent;
      }
      return '';
    }

    try {
      // Try to decode base64 data
      const cleanBase64 = csvDataResponse.csv_data_base64.trim().replace(/[\r\n\s]/g, '');
      const isBase64 = /^[A-Za-z0-9+/=]+$/.test(cleanBase64);

      if (isBase64) {
        return atob(cleanBase64);
      } else {
        return csvDataResponse.csv_data_base64;
      }
    } catch (error) {
      console.error('Error decoding CSV data:', error);
      return csvDataResponse.csv_data_base64;
    }
  }, [csvDataResponse, complaintData]);

  // Handle CSV data edit with React Query mutation
  const handleCSVEdit = useCallback(async (newCsvData: string, operationMetadata?: any) => {
    if (!id) return null;

    // Validate CSV data
    if (!newCsvData || newCsvData.trim() === '') {
      showError('Cannot save empty CSV data');
      return null;
    }

    try {
      const result = await updateCSVMutation.mutateAsync({
        csvData: newCsvData,
        complaintId: id,
        operationMetadata: {
          ...operationMetadata,
          complaintMetadata: complaintData?.metadata || complaintData?.graph_data?.metadata,
          complaintSummary: complaintData?.summary
        }
      });

      return result;
    } catch (error) {
      // Error handling is done in the mutation
      return null;
    }
  }, [id, updateCSVMutation, complaintData, showError]);

  // Handle CSV download
  const handleDownloadCSV = useCallback(async () => {
    if (!id) return;

    setIsDownloading(true);
    try {
      await complaintService.downloadCSV(id);
      showSuccess('Excel file downloaded successfully');
    } catch (error) {
      console.error('Error downloading Excel file:', error);
      showError('Failed to download Excel file');
    } finally {
      setIsDownloading(false);
    }
  }, [id, showSuccess, showError]);

  // Loading state
  const isLoading = complaintLoading || csvLoading || updateCSVMutation.isPending;

  // Error handling
  if (complaintError || csvError) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4">
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6 text-center">
            <h2 className="text-xl font-semibold text-red-400 mb-2">Error Loading Complaint</h2>
            <p className="text-red-300 mb-4">
              {complaintError?.message || csvError?.message || 'Failed to load complaint data'}
            </p>
            <div className="space-x-4">
              <Button onClick={() => refetchComplaint()} variant="outline">
                Retry
              </Button>
              <Button onClick={() => navigate('/dashboard')} variant="secondary">
                Back to Dashboard
              </Button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 p-4">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => navigate('/dashboard')}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <FiArrowLeft size={16} />
              <span>Back</span>
            </Button>
            <h1 className="text-2xl font-bold text-white">
              Complaint Details
            </h1>
            {complaintData?.complaint_id && (
              <span className="text-blue-300">#{complaintData.complaint_id}</span>
            )}
          </div>

          <div className="flex items-center space-x-3">
            <Button
              onClick={handleDownloadCSV}
              disabled={isDownloading || !csvData}
              variant="outline"
              size="sm"
              className="flex items-center space-x-2"
            >
              <FiDownload size={16} />
              <span>{isDownloading ? 'Downloading...' : 'Download Excel'}</span>
            </Button>
          </div>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-6 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-4"></div>
            <p className="text-blue-300">Loading complaint data...</p>
          </div>
        )}

        {/* CSV Table */}
        {csvData && !isLoading && (
          <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700/50 rounded-lg p-6">
            <div className="flex items-center space-x-2 mb-4">
              <FiFileText className="text-blue-400" size={20} />
              <h2 className="text-lg font-semibold text-white">Transaction Data</h2>
            </div>

            <ReactTableCSVPreview
              csvData={csvData}
              onSave={handleCSVEdit}
              onDownload={handleDownloadCSV}
              isLoading={updateCSVMutation.isPending}
              hiddenColumns={['Fraud Type', 'Sender Txn ID']}
              complaintMetadata={complaintData?.metadata || complaintData?.graph_data?.metadata || complaintData?.summary}
              onTotalsCalculated={(hold: number, withdrawal: number) => {
                // Handle totals if needed
              }}
            />
          </div>
        )}

        {/* No Data State */}
        {!csvData && !isLoading && (
          <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-6 text-center">
            <h2 className="text-xl font-semibold text-yellow-400 mb-2">No Transaction Data</h2>
            <p className="text-yellow-300">No CSV data or transactions found for this complaint.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default withLayout(ComplaintDetailComponent);
